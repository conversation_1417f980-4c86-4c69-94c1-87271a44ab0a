window.$HEADER.onLifecycle({ watch: '.how-to-depositi-video', selector: `#menu-wrapper`, onMount: (container, _, kill) => {
  const links = container.previousElementSibling.querySelectorAll('a')
  const blacklistedLinks = ['/pages/video-nasil-yapilir', '/pages/guncel-adres']
  

  for (const link of links) {
    const index = blacklistedLinks.findIndex(l => l === link.href.substring(window.location.origin.length + 3))
    if (index === -1) {
      blacklistedLinks[index] = null
      continue
    }

    link.style.display = 'none'
    blacklistedLinks[index] = link
  }

  const loginBtn = document.querySelector('.login-btn')
  const nextLink = blacklistedLinks[0]
  nextLink.insertAdjacentHTML('afterend', `
    <div class="call-me-dropdown">
      <div role="button" id="call-me-button" tabindex="0" class="call-me-header">
        <i class="el-icon-phone-outline"></i>
        <PERSON><PERSON>
        <i class="call-me-arrow el-icon-arrow-down"></i>
      </div>
      <div class="call-me-content" id="call-me-content">
        <div class="call-me-title">Arama Saati Seçin:</div>
        <div class="time-slots">
          <div class="time-slot" data-time-id="2" data-time="11:00 - 12:00">11:00 - 12:00</div>
          <div class="time-slot" data-time-id="3" data-time="12:00 - 13:00">12:00 - 13:00</div>
          <div class="time-slot" data-time-id="4" data-time="13:00 - 14:00">13:00 - 14:00</div>
          <div class="time-slot" data-time-id="5" data-time="14:00 - 15:00">14:00 - 15:00</div>
          <div class="time-slot" data-time-id="6" data-time="15:00 - 16:00">15:00 - 16:00</div>
          <div class="time-slot" data-time-id="7" data-time="16:00 - 17:00">16:00 - 17:00</div>
          <div class="time-slot" data-time-id="8" data-time="17:00 - 18:00">17:00 - 18:00</div>
        </div>
      </div>
    </div>
  `)

  // Add functionality for the call me dropdown
  const callMeButton = document.getElementById('call-me-button')
  const callMeContent = document.getElementById('call-me-content')
  const callMeArrow = callMeButton.querySelector('.call-me-arrow')
  const timeSlots = document.querySelectorAll('.time-slot')

  // Authentication state management
  let isAuthenticated = false
  let authCheckInterval = null

  // Show notification at top of page
  function showNotification(message, type = 'success') {
    // Remove any existing notifications
    const existingNotification = document.querySelector('.call-me-notification')
    if (existingNotification) {
      existingNotification.remove()
    }

    // Create notification element
    const notification = document.createElement('div')
    notification.className = `call-me-notification ${type}`
    notification.innerHTML = `
      <div class="notification-content">
        <i class="notification-icon ${type === 'success' ? 'el-icon-success' : 'el-icon-error'}"></i>
        <span class="notification-message">${message}</span>
        <button class="notification-close" onclick="this.parentElement.parentElement.remove()">
          <i class="el-icon-close"></i>
        </button>
      </div>
    `

    // Add styles
    notification.style.cssText = `
      position: fixed;
      top: 0;
      left: 0;
      right: 0;
      z-index: 10000;
      background: ${type === 'success' ? 'linear-gradient(135deg, #28a745 0%, #20c997 100%)' : 'linear-gradient(135deg, #dc3545 0%, #e74c3c 100%)'};
      color: white;
      padding: 12px 20px;
      box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
      transform: translateY(-100%);
      transition: transform 0.3s ease;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    `

    // Add mobile responsive styles
    if (window.innerWidth <= 768) {
      notification.style.padding = '10px 15px'
    }

    // Style the content
    const content = notification.querySelector('.notification-content')
    content.style.cssText = `
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 10px;
      max-width: 1200px;
      margin: 0 auto;
    `

    // Style the icon
    const icon = notification.querySelector('.notification-icon')
    icon.style.cssText = `
      font-size: 18px;
      flex-shrink: 0;
    `

    // Style the message
    const messageEl = notification.querySelector('.notification-message')
    messageEl.style.cssText = `
      flex: 1;
      text-align: center;
      font-size: ${window.innerWidth <= 768 ? '13px' : '14px'};
      font-weight: 500;
      line-height: 1.4;
    `

    // Style the close button
    const closeBtn = notification.querySelector('.notification-close')
    closeBtn.style.cssText = `
      background: none;
      border: none;
      color: white;
      cursor: pointer;
      padding: 4px;
      border-radius: 4px;
      transition: background-color 0.2s ease;
      flex-shrink: 0;
    `

    closeBtn.addEventListener('mouseenter', () => {
      closeBtn.style.backgroundColor = 'rgba(255, 255, 255, 0.1)'
    })

    closeBtn.addEventListener('mouseleave', () => {
      closeBtn.style.backgroundColor = 'transparent'
    })

    // Add to page
    document.body.appendChild(notification)

    // Animate in
    setTimeout(() => {
      notification.style.transform = 'translateY(0)'
    }, 10)

    // Auto remove after 3 seconds
    setTimeout(() => {
      notification.style.transform = 'translateY(-100%)'
      setTimeout(() => {
        if (notification.parentElement) {
          notification.remove()
        }
      }, 300)
    }, 3000)
  }

  // Check authentication status
  function checkAuthStatus() {
    const token = localStorage.getItem('s7oryO9STV')
    isAuthenticated = !!token
    return isAuthenticated
  }

  // Start periodic auth checking
  function startAuthPolling() {
    checkAuthStatus() // Initial check
    authCheckInterval = setInterval(checkAuthStatus, 1000) // Check every second
  }

  // Stop auth polling
  function stopAuthPolling() {
    if (authCheckInterval) {
      clearInterval(authCheckInterval)
      authCheckInterval = null
    }
  }

  // Send call request to API
  async function sendCallRequest(timeId, timeText) {
    const token = localStorage.getItem('s7oryO9STV')
    const customerCode = localStorage.getItem('customerCode')

    if (!token || !customerCode) {
      console.error('Missing authentication data')
      return false
    }

    const requestBody = {
      id: parseInt(timeId),
      type: 3,
      token: token,
      code: customerCode,
      time: timeText
    }

    try {
      const response = await fetch('https://pn17.pfnow.net/api/callService/demand', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestBody)
      })

      if (response.ok) {
        const result = await response.json()
        console.log('Call request sent successfully:', result)

        // Show success message
        showNotification('Arama talebiniz başarıyla gönderildi! En kısa sürede sizi arayacağız.', 'success')
        return true
      } else {
        console.error('Failed to send call request:', response.status)
        showNotification('Arama talebi gönderilirken bir hata oluştu. Lütfen tekrar deneyin.', 'error')
        return false
      }
    } catch (error) {
      console.error('Error sending call request:', error)
      showNotification('Arama talebi gönderilirken bir hata oluştu. Lütfen tekrar deneyin.', 'error')
      return false
    }
  }

  // Toggle dropdown
  function toggleDropdown() {
    const isOpen = callMeContent.classList.contains('open')

    if (isOpen) {
      callMeContent.classList.remove('open')
      callMeArrow.classList.remove('rotated')
    } else {
      callMeContent.classList.add('open')
      callMeArrow.classList.add('rotated')
    }
  }

  // Close dropdown when clicking outside
  function closeDropdown() {
    callMeContent.classList.remove('open')
    callMeArrow.classList.remove('rotated')
  }

  // Handle button click
  callMeButton.addEventListener('click', toggleDropdown)

  // Handle touch events for mobile
  callMeButton.addEventListener('touchstart', (e) => {
    e.preventDefault()
    toggleDropdown()
  })

  // Handle keyboard navigation
  callMeButton.addEventListener('keydown', (e) => {
    if (e.key === 'Enter' || e.key === ' ') {
      e.preventDefault()
      toggleDropdown()
    }
  })

  // Handle time slot selection
  timeSlots.forEach(slot => {
    slot.addEventListener('click', async () => {
      const timeId = slot.dataset.timeId
      const timeText = slot.dataset.time

      // Close dropdown first
      closeDropdown()

      // Check if user is authenticated
      if (checkAuthStatus()) {
        // User is authenticated, send API request
        const success = await sendCallRequest(timeId, timeText)
        if (success) {
          // Store selected time for reference
          localStorage.setItem('selectedCallTime', timeText)
        }
      } else {
        // User is not authenticated, store selection and trigger login
        localStorage.setItem('pendingCallTime', timeText)
        localStorage.setItem('pendingCallTimeId', timeId)

        // Click login button
        if (loginBtn) {
          loginBtn.click()
        }
      }
    })

    // Keyboard support for time slots
    slot.addEventListener('keydown', (e) => {
      if (e.key === 'Enter' || e.key === ' ') {
        e.preventDefault()
        slot.click()
      }
    })
  })

  // Close dropdown when clicking outside
  document.addEventListener('click', (e) => {
    if (!e.target.closest('.call-me-dropdown')) {
      closeDropdown()
    }
  })

  // Handle pending call requests after login
  async function handlePendingCallRequest() {
    const pendingTimeId = localStorage.getItem('pendingCallTimeId')
    const pendingTimeText = localStorage.getItem('pendingCallTime')

    if (pendingTimeId && pendingTimeText && checkAuthStatus()) {
      // Clear pending data
      localStorage.removeItem('pendingCallTimeId')
      localStorage.removeItem('pendingCallTime')

      // Send the call request
      await sendCallRequest(pendingTimeId, pendingTimeText)
    }
  }

  // Start authentication polling and check for pending requests
  function initializeCallMe() {
    checkAuthStatus() // Initial check

    // Start polling for auth changes
    authCheckInterval = setInterval(() => {
      const wasAuthenticated = isAuthenticated
      checkAuthStatus()

      // If user just logged in, handle pending requests
      if (!wasAuthenticated && isAuthenticated) {
        handlePendingCallRequest()
      }
    }, 1000)

    // Check for pending requests on initialization
    if (isAuthenticated) {
      handlePendingCallRequest()
    }
  }

  // Cleanup function
  function cleanupCallMe() {
    if (authCheckInterval) {
      clearInterval(authCheckInterval)
      authCheckInterval = null
    }
  }

  // Initialize the call me functionality
  initializeCallMe()

  // Cleanup on page unload
  window.addEventListener('beforeunload', cleanupCallMe)
}})
