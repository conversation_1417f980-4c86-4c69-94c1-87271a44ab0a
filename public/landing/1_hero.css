/* Hero Section Styles */
.hero-section {
  padding: 12px 0 0 0;
  position: relative;
  z-index: 1;
  background: linear-gradient(135deg,
    rgba(var(--secondary-color), 0.08) 0%,
    rgba(var(--primary-color), 0.12) 50%,
    rgba(var(--secondary-color), 0.08) 100%);
  min-height: 460px;
  display: flex;
  align-items: center;
}

.hero-container {
  max-width: 1500px;
  margin: 0 auto;
  padding: 0 20px;
  position: relative;
  z-index: 1;
  width: 100%;
}

.hero-content {
  display: grid;
  grid-template-columns: 7fr 3fr;
  gap: 14px;
  align-items: stretch;
  min-height: 420px;
}

/* Slider Column (3/4) */
.hero-slider-column {
  position: relative;
}

.hero-slider {
  background: linear-gradient(135deg,
    rgba(var(--secondary-color), 0.6) 0%,
    rgba(var(--secondary-color), 0.4) 100%);
  border-radius: 16px;
  overflow: hidden;
  position: relative;
  height: 420px;
  border: 1px solid rgba(var(--primary-color), 0.2);
  box-shadow:
    0 8px 32px rgba(0, 0, 0, 0.3),
    0 4px 16px rgba(0, 0, 0, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.1),
    inset 0 -1px 0 rgba(0, 0, 0, 0.15);
  backdrop-filter: blur(12px);
  transition: all 0.3s ease;
}

.hero-slider:hover {
  transform: translateY(-1px);
  box-shadow:
    0 12px 40px rgba(0, 0, 0, 0.35),
    0 6px 20px rgba(0, 0, 0, 0.25),
    inset 0 1px 0 rgba(255, 255, 255, 0.12),
    inset 0 -1px 0 rgba(0, 0, 0, 0.2);
}

.slider-container {
  position: relative;
  width: 100%;
  height: 100%;
  overflow: hidden;
}

.slider-track {
  display: flex;
  width: 100%;
  height: 100%;
  transition: transform 0.6s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: grab;
  will-change: transform;
}

.slider-track.dragging {
  transition: none;
  cursor: grabbing;
}

.slide {
  flex: 0 0 100%;
  width: 100%;
  height: 100%;
  cursor: pointer;
  position: relative;
}

.slide-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  object-position: center;
  border-radius: 16px;
  transition: transform 0.3s ease;
}

.slide:hover .slide-image {
  transform: scale(1.01);
}

/* Mobile optimization for hero slider images */
@media (max-width: 768px) {
  .slide-image {
    object-position: left center;
  }
}

/* Slider Dots */
.slider-dots {
  position: absolute;
  bottom: 12px;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  flex-direction: row;
  gap: 10px;
  z-index: 10;
  background: rgba(0, 0, 0, 0.5);
  padding: 8px 14px;
  border-radius: 16px;
  backdrop-filter: blur(12px);
  border: 1px solid rgba(255, 255, 255, 0.15);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.4);
}

.slider-dot {
  width: 10px;
  height: 10px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.5);
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border: 1px solid rgba(255, 255, 255, 0.7);
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.3);
  position: relative;
}

.slider-dot::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background: rgba(var(--primary-color), 0.4);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  transition: all 0.3s ease;
}

.slider-dot.active {
  background: rgb(var(--primary-color));
  border-color: rgb(var(--primary-color));
  transform: scale(1.2);
  box-shadow:
    0 0 12px rgba(var(--primary-color), 0.8),
    0 2px 8px rgba(0, 0, 0, 0.4);
}

.slider-dot.active::before {
  width: 100%;
  height: 100%;
}

.slider-dot:hover {
  background: rgba(var(--primary-color), 0.8);
  border-color: rgba(var(--primary-color), 0.9);
  transform: scale(1.1);
  box-shadow:
    0 0 10px rgba(var(--primary-color), 0.6),
    0 2px 6px rgba(0, 0, 0, 0.3);
}

/* CTA Column (1/4) */
.hero-cta-column {
  position: relative;
  background: linear-gradient(135deg,
    rgba(var(--secondary-color), 0.9) 0%,
    rgba(var(--secondary-color), 0.75) 50%,
    rgba(var(--secondary-color), 0.65) 100%);
  border-radius: 16px;
  padding: 32px 24px;
  border: 1px solid rgba(var(--primary-color), 0.2);
  box-shadow:
    0 8px 32px rgba(0, 0, 0, 0.3),
    0 4px 16px rgba(0, 0, 0, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.1),
    inset 0 -1px 0 rgba(0, 0, 0, 0.15);
  overflow: hidden;
  backdrop-filter: blur(12px);
  transition: all 0.3s ease;
}

.hero-cta-column:hover {
  transform: translateY(-1px);
  box-shadow:
    0 12px 40px rgba(0, 0, 0, 0.35),
    0 6px 20px rgba(0, 0, 0, 0.25),
    inset 0 1px 0 rgba(255, 255, 255, 0.12),
    inset 0 -1px 0 rgba(0, 0, 0, 0.2);
}

/* CTA Background Effects */
.hero-cta-column::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    /* Subtle geometric pattern */
    repeating-linear-gradient(45deg,
      transparent 0px,
      rgba(var(--primary-color), 0.08) 1px,
      transparent 2px,
      transparent 30px),
    repeating-linear-gradient(-45deg,
      transparent 0px,
      rgba(var(--primary-color), 0.05) 1px,
      transparent 2px,
      transparent 40px),
    /* Enhanced corner glows */
    radial-gradient(circle at top right, rgba(var(--primary-color), 0.15) 0%, transparent 50%),
    radial-gradient(circle at bottom left, rgba(var(--primary-color), 0.12) 0%, transparent 50%),
    radial-gradient(circle at center, rgba(var(--primary-color), 0.06) 0%, transparent 70%);
  pointer-events: none;
  z-index: 1;
  animation: ctaGlow 6s ease-in-out infinite;
}

.cta-content {
  position: relative;
  z-index: 2;
  text-align: center;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  gap: 16px;
}

.hero-title {
  font-size: 2rem;
  font-weight: 800;
  color: rgb(var(--primary-color));
  margin: 0;
  text-shadow:
    0 2px 8px rgba(0, 0, 0, 0.4),
    0 0 16px rgba(var(--primary-color), 0.4);
  line-height: 1.1;
  background: linear-gradient(135deg,
    rgb(var(--primary-color)) 0%,
    rgba(var(--primary-color), 0.8) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.hero-description {
  font-size: 0.95rem;
  color: rgba(255, 255, 255, 0.92);
  line-height: 1.4;
  margin: 0;
  text-shadow: 0 1px 4px rgba(0, 0, 0, 0.4);
  font-weight: 500;
}

.hero-cta-button {
  background: linear-gradient(135deg,
    rgb(var(--primary-color)) 0%,
    rgba(var(--primary-color), 0.9) 50%,
    rgba(var(--primary-color), 0.8) 100%);
  border: none;
  border-radius: 14px;
  padding: 14px 28px;
  font-size: 1.05rem;
  font-weight: 700;
  color: rgb(var(--secondary-color));
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  text-transform: uppercase;
  letter-spacing: 0.6px;
  box-shadow:
    0 4px 16px rgba(var(--primary-color), 0.4),
    0 2px 8px rgba(0, 0, 0, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
  position: relative;
  overflow: hidden;
  border: 1px solid rgba(var(--primary-color), 0.3);
}

.hero-cta-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s ease;
}

.hero-cta-button:hover::before {
  left: 100%;
}

.hero-cta-button:hover {
  transform: translateY(-2px) scale(1.02);
  box-shadow:
    0 6px 20px rgba(var(--primary-color), 0.5),
    0 3px 12px rgba(0, 0, 0, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.3);
  background: linear-gradient(135deg,
    rgba(var(--primary-color), 1.1) 0%,
    rgb(var(--primary-color)) 50%,
    rgba(var(--primary-color), 0.9) 100%);
}

.hero-cta-button:active {
  transform: translateY(-1px) scale(1.01);
  transition: all 0.1s ease;
}

.cta-text {
  position: relative;
  z-index: 2;
}

.cta-icon {
  font-size: 1.2rem;
  position: relative;
  z-index: 2;
}

/* Animations */
@keyframes ctaGlow {
  0%, 100% {
    opacity: 0.8;
    transform: scale(1);
  }
  25% {
    opacity: 0.9;
    transform: scale(1.002);
  }
  50% {
    opacity: 1;
    transform: scale(1.005);
  }
  75% {
    opacity: 0.9;
    transform: scale(1.002);
  }
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(15px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.hero-content {
  animation: slideIn 0.6s ease-out;
}

/* Loading State */
.hero-slider.loading {
  background: rgba(var(--secondary-color), 0.2);
}

.hero-slider.loading::after {
  content: 'Slaytlar yükleniyor...';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: rgba(255, 255, 255, 0.7);
  font-size: 1.1rem;
  z-index: 5;
}

/* Error State */
.hero-slider.error::after {
  content: 'Slaytlar yüklenemedi';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: rgba(255, 100, 100, 0.8);
  font-size: 1.1rem;
  z-index: 5;
}

/* Responsive Design */
@media (max-width: 1200px) {
  .hero-section {
    min-height: 440px;
  }

  .hero-container {
    padding: 0 18px;
  }

  .hero-content {
    gap: 12px;
    min-height: 400px;
  }

  .hero-slider {
    height: 400px;
    border-radius: 14px;
  }

  .slide-image {
    border-radius: 14px;
  }

  .hero-cta-column {
    padding: 28px 20px;
    border-radius: 14px;
  }

  .hero-title {
    font-size: 1.8rem;
  }

  .hero-description {
    font-size: 0.9rem;
  }

  .hero-cta-button {
    padding: 13px 26px;
    font-size: 1rem;
    border-radius: 12px;
  }

  .slider-dots {
    bottom: 10px;
    padding: 6px 12px;
    border-radius: 14px;
  }

  .slider-dot {
    width: 9px;
    height: 9px;
  }
}

@media (max-width: 1024px) {
  .hero-section {
    min-height: 420px;
  }

  .hero-content {
    grid-template-columns: 2fr 1fr;
    gap: 10px;
    min-height: 380px;
  }

  .hero-slider {
    height: 380px;
    border-radius: 12px;
  }

  .slide-image {
    border-radius: 12px;
  }

  .hero-cta-column {
    padding: 24px 18px;
    border-radius: 12px;
  }

  .hero-title {
    font-size: 1.6rem;
    line-height: 1.1;
  }

  .hero-description {
    font-size: 0.85rem;
    line-height: 1.3;
  }

  .hero-cta-button {
    padding: 12px 24px;
    font-size: 0.95rem;
    border-radius: 10px;
  }

  .slider-dots {
    bottom: 8px;
    gap: 8px;
    padding: 5px 10px;
    border-radius: 12px;
  }

  .slider-dot {
    width: 8px;
    height: 8px;
  }
}

@media (max-width: 768px) {
  .hero-section {
    padding: 10px 0;
    min-height: 380px;
    background: linear-gradient(135deg,
      rgba(var(--secondary-color), 0.1) 0%,
      rgba(var(--primary-color), 0.15) 50%,
      rgba(var(--secondary-color), 0.1) 100%);
  }

  .hero-container {
    padding: 0 14px;
  }

  .hero-content {
    grid-template-columns: 1fr;
    gap: 12px;
    min-height: 360px;
  }

  .hero-slider {
    height: 280px;
    border-radius: 12px;
  }

  .slide-image {
    border-radius: 12px;
  }

  .hero-cta-column {
    padding: 20px 16px;
    border-radius: 12px;
  }

  .hero-title {
    font-size: 1.4rem;
    line-height: 1.1;
  }

  .hero-description {
    font-size: 0.8rem;
    line-height: 1.3;
  }

  .hero-cta-button {
    padding: 12px 24px;
    font-size: 0.9rem;
    border-radius: 10px;
  }

  .slider-dots {
    bottom: 8px;
    gap: 6px;
    padding: 4px 8px;
    border-radius: 10px;
  }

  .slider-dot {
    width: 8px;
    height: 8px;
  }
}

@media (max-width: 480px) {
  .hero-section {
    padding: 10px 0;
  }

  .hero-container {
    padding: 0 12px;
  }

  .hero-content {
    gap: 16px;
  }

  .hero-slider {
    height: 240px;
    border-radius: 16px;
  }

  .hero-cta-column {
    padding: 20px 16px;
    border-radius: 16px;
  }

  .hero-title {
    font-size: 1.3rem;
  }

  .hero-description {
    font-size: 0.8rem;
  }

  .hero-cta-button {
    padding: 12px 24px;
    font-size: 0.9rem;
    border-radius: 12px;
  }

  .slider-dots {
    bottom: 12px;
    gap: 8px;
    padding: 6px 12px;
  }

  .slider-dot {
    width: 10px;
    height: 10px;
  }
}

/* User Content Styles */
.user-content {
  position: relative;
  z-index: 2;
  text-align: center;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  gap: 16px;
}

.user-welcome {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.user-title {
  font-size: 2rem;
  font-weight: 700;
  color: rgb(var(--primary-color));
  margin: 0;
  text-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
  line-height: 1.2;
}

.user-status {
  display: flex;
  flex-direction: column;
  gap: 12px;
  align-items: center;
}

.user-rank {
  display: flex;
  justify-content: center;
}

.rank-badge {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  background: rgba(0, 0, 0, 0.3);
  border: 2px solid #CD7F32;
  border-radius: 25px;
  backdrop-filter: blur(10px);
  color: #CD7F32;
  font-weight: 600;
  font-size: 0.9rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);
}

.rank-icon {
  font-size: 1.2rem;
}

.user-vip-status {
  display: flex;
  justify-content: center;
}

.vip-badge {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  background: linear-gradient(45deg, #FFD700, #FFA500);
  border: 2px solid #FFD700;
  border-radius: 25px;
  color: #1A1A1A;
  font-weight: 700;
  font-size: 0.9rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  box-shadow:
    0 4px 16px rgba(255, 215, 0, 0.3),
    0 0 20px rgba(255, 215, 0, 0.2);
  animation: vipGlow 2s ease-in-out infinite;
}

.vip-icon {
  font-size: 1.2rem;
}

.user-description {
  font-size: 1rem;
  color: rgba(255, 255, 255, 0.9);
  line-height: 1.6;
  margin: 0;
  text-shadow: 0 1px 4px rgba(0, 0, 0, 0.3);
}

.user-actions {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.hero-cta-button.primary {
  background: linear-gradient(45deg, rgb(var(--primary-color)), rgba(var(--primary-color), 0.8));
}

.hero-cta-button.secondary {
  background: linear-gradient(45deg, #FFD700, #FFA500);
  color: #1A1A1A;
  box-shadow: 0 4px 16px rgba(255, 215, 0, 0.3);
}

.hero-cta-button.secondary:hover {
  background: linear-gradient(45deg, #FFA500, #FFD700);
  box-shadow: 0 8px 24px rgba(255, 215, 0, 0.4);
}

/* VIP Glow Animation */
@keyframes vipGlow {
  0%, 100% {
    box-shadow:
      0 4px 16px rgba(255, 215, 0, 0.3),
      0 0 20px rgba(255, 215, 0, 0.2);
  }
  50% {
    box-shadow:
      0 4px 20px rgba(255, 215, 0, 0.4),
      0 0 30px rgba(255, 215, 0, 0.3);
  }
}

/* Responsive Design for User Content */
@media (max-width: 1024px) {
  .user-title {
    font-size: 1.7rem;
  }

  .rank-badge, .vip-badge {
    font-size: 0.8rem;
    padding: 6px 12px;
  }

  .rank-icon, .vip-icon {
    font-size: 1rem;
  }
}

@media (max-width: 768px) {
  .user-title {
    font-size: 1.4rem;
  }

  .user-status {
    gap: 10px;
  }

  .rank-badge, .vip-badge {
    font-size: 0.75rem;
    padding: 6px 10px;
  }

  .user-description {
    font-size: 0.85rem;
  }
}

@media (max-width: 480px) {
  .user-title {
    font-size: 1.2rem;
  }

  .user-actions {
    gap: 10px;
  }

  .rank-badge, .vip-badge {
    font-size: 0.7rem;
    padding: 5px 8px;
  }

  .user-description {
    font-size: 0.8rem;
  }
}

/* Last Played Slot Styles */
.last-played-section {
  display: flex;
  flex-direction: column;
  gap: 4px;
  align-items: center;
}

.last-played-game {
  display: flex;
  align-items: center;
  gap: 12px;
  background: rgba(0, 0, 0, 0.3);
  border-radius: 12px;
  padding: 12px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(var(--primary-color), 0.2);
  width: 100%;
  max-width: 280px;
  cursor: pointer;
  transition: transform 0.2s ease, border-color 0.2s ease;
}

.last-played-game:hover {
  transform: scale(1.02);
  border-color: rgba(var(--primary-color), 0.4);
}

.game-image-container {
  flex-shrink: 0;
  width: 60px;
  height: 60px;
  border-radius: 8px;
  overflow: hidden;
  background: rgba(var(--primary-color), 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.last-played-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 8px;
}

.play-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 8px;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.last-played-game:hover .play-overlay {
  opacity: 1;
}

.play-button {
  background: rgba(var(--primary-color), 0.9);
  border: none;
  border-radius: 50%;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

.play-button:hover {
  background: rgb(var(--primary-color));
  transform: scale(1.1);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.4);
}

.play-icon {
  color: white;
  font-size: 12px;
  margin-left: 2px; /* Slight offset to center the triangle visually */
}

.play-again-text {
  font-size: 1rem;
  color: rgba(255, 255, 255, 0.7);
  margin: 0;
  text-align: center;
  font-weight: 400;
}

.play-again-text {
  font-size: 0.75rem;
  color: rgba(255, 255, 255, 0.7);
  margin: 0;
  text-align: center;
  font-weight: 400;
  margin-top: 8px;
}

.game-info {
  flex: 1;
  min-width: 0;
}

.game-name {
  font-size: 0.9rem;
  font-weight: 600;
  color: rgb(var(--primary-color));
  margin: 0 0 4px 0;
  line-height: 1.2;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.game-provider {
  font-size: 0.75rem;
  color: rgba(255, 255, 255, 0.7);
  margin: 0;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}



/* Responsive Design for Last Played Slot */
@media (max-width: 1024px) {
  .last-played-game {
    max-width: 260px;
    padding: 10px;
    gap: 10px;
  }

  .game-image-container {
    width: 50px;
    height: 50px;
  }

  .game-name {
    font-size: 0.85rem;
  }

  .game-provider {
    font-size: 0.7rem;
  }

  .play-again-text {
    font-size: 0.75rem;
  }

  .play-again-text {
    font-size: 0.7rem;
  }
}

@media (max-width: 768px) {
  .last-played-section {
    gap: 14px;
    padding: 16px 0;
  }

  .last-played-game {
    max-width: 240px;
    padding: 8px;
  }

  .game-image-container {
    width: 45px;
    height: 45px;
  }

  .game-name {
    font-size: 0.8rem;
  }

  .game-provider {
    font-size: 0.65rem;
  }

  .play-again-text {
    font-size: 0.7rem;
  }

  .play-again-text {
    font-size: 0.65rem;
  }
}

@media (max-width: 480px) {
  .last-played-section {
    gap: 12px;
    padding: 14px 0;
  }

  .last-played-game {
    max-width: 220px;
    padding: 8px;
  }

  .game-image-container {
    width: 40px;
    height: 40px;
  }

  .game-name {
    font-size: 0.75rem;
  }

  .game-provider {
    font-size: 0.6rem;
  }

  .play-again-text {
    font-size: 0.65rem;
  }
}
