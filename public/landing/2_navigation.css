/* Navigation Section Styles */
.navigation {
  position: relative;
  padding: 16px 0;
}

.navigation-container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 20px;
  position: relative;
  z-index: 1;
}

.navigation-tiles {
  display: grid;
  grid-template-columns: repeat(6, 1fr);
  gap: 16px;
  overflow-x: auto;
  padding: 8px;
  scrollbar-width: none;
  -ms-overflow-style: none;
}

.navigation-tiles::-webkit-scrollbar {
  display: none;
}

.nav-image {
  width: 100%;
  height: auto;
  object-fit: contain;
  cursor: pointer;
  transition: all 0.3s ease;
  opacity: 0.8;
  display: block;
  filter: hue-rotate(12deg) brightness(1.2);
}

.nav-image:hover {
  opacity: 1;
  transform: scale(1.02);
}

.nav-image:active {
  transform: scale(0.95);
}

/* Focus styles for accessibility */
.nav-image:focus {
  outline: 2px solid rgb(var(--primary-color));
  outline-offset: 2px;
}

/* Responsive Design */
@media (max-width: 1200px) {
  .navigation-tiles {
    gap: 14px;
  }
}

@media (max-width: 1024px) {
  .navigation-tiles {
    gap: 12px;
  }
}

@media (max-width: 768px) {
  .navigation {
    padding: 20px 0;
  }

  .navigation-container {
    padding: 0 15px;
  }

  .navigation-tiles {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 15px;
  }

  .nav-image {
    width: 100%;
    height: auto;
  }
}

@media (max-width: 480px) {
  .navigation {
    padding: 15px 0;
  }

  .navigation-tiles {
    gap: 12px;
  }
}

/* Enhanced Focus Styles for Accessibility */
.nav-image:focus-visible {
  outline: 3px solid rgb(var(--primary-color));
  outline-offset: 3px;
  opacity: 1;
  transform: scale(1.05);
}
