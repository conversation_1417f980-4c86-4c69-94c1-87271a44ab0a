/* Wins Section Styles */
.wins-section {}

.wins-section .wins-container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 24px;
  position: relative;
  z-index: 1;
}

.wins-section .wins-header {
  text-align: center;
  margin-bottom: 30px;
}

/* Top Wins Marquee */
.wins-section .top-wins-marquee {
  background: rgba(0, 0, 0, 0.3);
  border-radius: 16px;
  padding: 20px;
  margin-bottom: 30px;
  border: 1px solid rgba(var(--primary-color), 0.1);
  overflow: hidden;
  position: relative;
}

.wins-section .top-wins-title {
  color: rgb(var(--primary-color));
  font-size: 1.2rem;
  font-weight: 600;
  margin: 0 0 15px 0;
  text-align: center;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.wins-section .top-wins-track {
  display: flex;
  gap: 12px;
  animation: marqueeScroll 60s linear infinite;
  white-space: nowrap;
  width: max-content;
}

.wins-section .top-win-item {
  flex-shrink: 0;
  background: rgba(var(--primary-color), 0.1);
  border-radius: 12px;
  padding: 15px 20px;
  color: rgb(var(--primary-color));
  font-weight: 600;
  font-size: 1rem;
  border: 1px solid rgba(var(--primary-color), 0.2);
  min-width: 200px;
  text-align: center;
}

@keyframes marqueeScroll {
  0% {
    transform: translateX(0);
  }

  100% {
    transform: translateX(-33.333%);
  }
}

.wins-section .wins-content {
  width: 100%;
}

.wins-section .wins-grid {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  gap: 20px;
  align-items: start;
}

.wins-section .wins-column {
  background: rgba(0, 0, 0, 0.3);
  border-radius: 16px;
  padding: 20px;
  border: 1px solid rgba(var(--primary-color), 0.1);
}

.wins-section .wins-table-title {
  color: rgb(var(--primary-color));
  font-size: 1.2rem;
  font-weight: 600;
  margin-bottom: 15px;
  margin-top: 0;
  text-align: center;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.wins-section .wins-table-container {
  overflow-x: auto;
  border-radius: 12px;
  background: rgba(0, 0, 0, 0.2);
}

.wins-section .wins-table {
  width: 100%;
  border-collapse: collapse;
  font-size: 0.9rem;
  table-layout: fixed;
}

.wins-section .wins-table thead {
  background: rgba(var(--primary-color), 0.1);
}

.wins-section .wins-table th {
  padding: 12px 8px;
  text-align: left;
  color: rgb(var(--primary-color));
  font-weight: 600;
  text-transform: uppercase;
  font-size: 0.8rem;
  letter-spacing: 0.5px;
  border-bottom: 2px solid rgba(var(--primary-color), 0.2);
}

.wins-section .wins-table td {
  padding: 10px 8px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  color: #ffffff;
  vertical-align: middle;
}

.wins-section .wins-table td:first-child {
  width: 50%;
  min-width: 180px;
}

.wins-section .wins-table td:nth-child(2) {
  width: 20%;
  min-width: 80px;
}

.wins-section .wins-table td:nth-child(3) {
  width: 30%;
  min-width: 100px;
}

.wins-section .wins-table tbody tr:hover {
  background: rgba(var(--primary-color), 0.05);
}

.wins-section .wins-table tbody tr:last-child td {
  border-bottom: none;
}

.wins-section .game-cell {
  padding: 10px 8px !important;
  vertical-align: middle !important;
  width: 50%;
  min-width: 180px;
}

.wins-section .game-info {
  display: flex;
  align-items: center;
  gap: 10px;
  width: 100%;
}

.wins-section .game-image {
  width: 40px;
  height: 30px;
  border-radius: 6px;
  object-fit: cover;
  object-position: center top;
  border: 1px solid rgba(var(--primary-color), 0.2);
  flex-shrink: 0;
  display: block;
  background-color: rgba(var(--secondary-color), 0.3);
}

.wins-section .game-name {
  font-weight: 500;
  color: #ffffff;
  font-size: 0.85rem;
  line-height: 1.2;
  max-width: 120px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  flex: 1;
}

.wins-section .win-amount {
  font-weight: 600;
  color: rgb(var(--primary-color));
  font-size: 0.9rem;
}

.wins-section .player-name {
  color: #ffffff;
  font-size: 0.85rem;
  font-weight: 500;
}

.wins-section .win-date {
  color: #cccccc;
  font-size: 0.8rem;
}

.wins-section .loading-row td {
  text-align: center;
  color: #cccccc;
  font-style: italic;
  padding: 20px;
}

.wins-section .error-row td {
  text-align: center;
  color: #ff6b6b;
  font-style: italic;
  padding: 20px;
}

.wins-section .no-data-row td {
  text-align: center;
  color: #cccccc;
  font-style: italic;
  padding: 20px;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .wins-section .wins-container {
    padding: 0 20px;
  }

  .wins-section .wins-grid {
    grid-template-columns: 1fr 1fr;
    gap: 15px;
  }

  .wins-section .wins-column:last-child {
    grid-column: span 2;
  }
}

@media (max-width: 768px) {
  .wins-section .wins-container {
    padding: 0 16px;
  }

  .wins-section .wins-grid {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .wins-section .wins-column:last-child {
    grid-column: span 1;
  }

  .wins-section {
    padding: 15px 0;
    margin: 10px 0;
  }

  .wins-section .top-wins-marquee {
    padding: 15px;
  }

  .wins-section .top-wins-title {
    font-size: 1.1rem;
    margin-bottom: 12px;
  }

  .wins-section .top-win-item {
    padding: 12px 16px;
    font-size: 0.9rem;
  }

  .wins-section .wins-table {
    font-size: 0.8rem;
  }

  .wins-section .wins-table th,
  .wins-section .wins-table td {
    padding: 8px 6px;
  }

  .wins-section .game-image {
    width: 35px;
    height: 26px;
  }

  .wins-section .game-name {
    font-size: 0.8rem;
  }

  .wins-section .win-amount {
    font-size: 0.85rem;
  }

  .wins-tabs-container {
    margin: 0 0 20px 0;
    background: rgba(0, 0, 0, 0.25);
    border-radius: 14px;
    padding: 10px 0 0 0;
  }

  .wins-tabs-btn-group {
    display: flex;
    justify-content: space-around;
    margin-bottom: 10px;
    gap: 0;
    border-bottom: 1px solid rgba(var(--primary-color), 0.15);
  }

  .wins-tab-btn {
    flex: 1 1 0;
    background: none;
    border: none;
    outline: none !important;
    box-shadow: none !important;
    padding: 12px 0 10px 0;
    font-size: 1rem;
    color: #fff;
    font-weight: 600;
    cursor: pointer;
    border-bottom: 2px solid transparent;
    transition: border-color 0.2s, color 0.2s;
    border-radius: 0;
    position: relative;
    overflow: hidden;
  }

  .wins-tab-btn::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    filter: blur(12px);
    z-index: 0;
  }

  .wins-tab-btn:focus,
  .wins-tab-btn:active {
    outline: none !important;
    box-shadow: none !important;
    background: none !important;
  }

  .wins-tab-btn.active {
    color: rgb(var(--primary-color));
    border-bottom: 2.5px solid rgb(var(--primary-color));
  }

  .wins-tabs-content {
    width: 100%;
  }

  .wins-tab-panel {
    display: none;
    width: 100%;
  }

  .wins-tab-panel[style*="display: block"] {
    display: block;
  }
}

@media (max-width: 480px) {

  /* .wins-section .wins-table th:last-child,
  .wins-section .wins-table td:last-child {
    display: none;
  } */

  .wins-section .wins-table th:first-child,
  .wins-section .wins-table td:first-child {
    width: 180px;
  }

  .wins-section .game-info {
    gap: 8px;
  }

  .wins-section .game-image {
    width: 30px;
    height: 22px;
  }

  .wins-section .game-name {
    flex-grow: 1;
  }

  .wins-section .top-wins-title {
    font-size: 1rem;
    margin-bottom: 10px;
  }

  .wins-section .top-win-item {
    padding: 10px 12px;
    font-size: 0.8rem;
  }
}

@media (min-width: 769px) {
  .wins-tabs-container {
    margin: 0 0 30px 0;
    background: rgba(0, 0, 0, 0.18);
    border-radius: 16px;
    padding: 18px 0 0 0;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
  }

  .wins-tabs-btn-group {
    display: flex;
    justify-content: center;
    margin-bottom: 18px;
    gap: 0;
    border-bottom: 1.5px solid rgba(var(--primary-color), 0.18);
  }

  .wins-tab-btn {
    flex: 1 1 0;
    background: none;
    border: none;
    outline: none !important;
    box-shadow: none !important;
    padding: 16px 0 14px 0;
    font-size: 1.08rem;
    color: #fff;
    font-weight: 700;
    cursor: pointer;
    border-bottom: 3px solid transparent;
    transition: border-color 0.2s, color 0.2s, background 0.2s;
    border-radius: 0;
    max-width: 220px;
    letter-spacing: 0.5px;
    position: relative;
    overflow: hidden;
  }

  .wins-tab-btn::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    background: linear-gradient(to bottom, rgba(24, 24, 32, 0.45) 0%, rgba(24, 24, 32, 0.0) 80%);
    filter: blur(12px);
    z-index: 0;
  }

  .wins-tab-btn:focus,
  .wins-tab-btn:active {
    outline: none !important;
    box-shadow: none !important;
    background: none !important;
  }

  .wins-tab-btn.active {
    color: rgb(var(--primary-color));
    border-bottom: 3px solid rgb(var(--primary-color));
    background: rgba(var(--primary-color), 0.09);
  }

  .wins-tabs-content {
    width: 100%;
  }

  .wins-tab-panel {
    display: none;
    width: 100%;
  }

  .wins-tab-panel[style*="display: block"] {
    display: block;
  }
}
