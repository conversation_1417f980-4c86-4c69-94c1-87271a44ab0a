// Common Game Slider JavaScript - Pure Display Component
window.GameSlider = function(config) {
  const {
    sectionId,
    trackId,
    prevArrowId,
    nextArrowId,
    games, // Items to display (required)
    createCardFunction,
    navigationPath,
    noItemsMessage = `No items found at the moment.`
  } = config;

  function rafThrottle(fn) {
    let locked = false;
    return (...args) => {
      if (locked) return;
      locked = true;
      requestAnimationFrame(() => {
        fn(...args);
        locked = false;
      });
    };
  }

  const gameTrack = document.getElementById(trackId);
  const prevArrow = document.querySelector(`.${sectionId} #${prevArrowId}`);
  const nextArrow = document.querySelector(`.${sectionId} #${nextArrowId}`);
  const sectionElement = document.querySelector(`.${sectionId}`);

  // Check if elements exist
  if (!gameTrack || !prevArrow || !nextArrow || !sectionElement) {
    console.error(`${sectionId}: Required DOM elements not found`, {
      gameTrack: !!gameTrack,
      prevArrow: !!prevArrow,
      nextArrow: !!nextArrow,
      sectionElement: !!sectionElement
    });
    return;
  }
  
  let currentIndex = 0;
  let totalItems = 0;
  let cardWidth = 296; // Default: 280px card + 16px gap

  // Mouse drag state
  let isDragging = false;
  let dragStartX = 0;
  let dragStartTranslateX = 0;
  let lastDragX = 0;
  let dragVelocity = 0;
  let dragStartTime = 0;
  let hasDragged = false; // Track if user actually dragged (vs just clicked)

  // Touch gesture detection state
  let touchStartX = 0;
  let touchStartY = 0;
  let gestureDetected = false;
  let isVerticalGesture = false;
  let touchStartedOnSlider = false; // Track if touch started on slider
  const GESTURE_THRESHOLD = 10; // Minimum movement to detect gesture direction

  // Throtthled functions for performance optimization
  const rafThrottledUpdateCardWidth = rafThrottle(updateCardWidth);
  const rafThrottledUpdateDrag = rafThrottle(updateDragInternal);
  const rafThrottledUpdateSliderControls = rafThrottle(updateSliderControls);
  
  // Display items
  function renderGames() {
    // Clear existing content
    gameTrack.innerHTML = ``;

    if (!games || games.length === 0) {
      // Create no items message
      const errorDiv = document.createElement(`div`);
      errorDiv.className = `error-message`;
      errorDiv.textContent = noItemsMessage;
      gameTrack.appendChild(errorDiv);
      return;
    }

    // Create and append item cards
    games.forEach(item => {
      const itemCard = createCardFunction(item);

      // Add click handler with scroll detection
      let isScrolling = false;
      let touchStartY = 0;

      const handleCardNavigation = function(e) {
        // Prevent navigation if user was dragging
        if (hasDragged) {
          e.preventDefault();
          return;
        }

        const itemId = this.dataset.gameId || this.dataset.providerId;
        const itemCode = this.dataset.providerCode;
        let path = itemCode ?
          navigationPath.replace(`:code`, itemCode) :
          navigationPath.replace(`:id`, itemId);
        if (item.isLive) {
          path = path.replace(':limit', item.vendorLimitGroups[0].vendorLimitId);
        }
        $LANDING.navigate(path);
      };

      itemCard.addEventListener(`click`, handleCardNavigation);

      // Add touch support with scroll detection
      itemCard.addEventListener(`touchstart`, function(e) {
        isScrolling = false;
        touchStartY = e.touches[0].clientY;
      });

      itemCard.addEventListener(`touchmove`, function(e) {
        const touchMoveY = e.touches[0].clientY;
        const deltaY = Math.abs(touchMoveY - touchStartY);

        // If moved more than 10px vertically, consider it scrolling
        if (deltaY > 10) {
          isScrolling = true;
        }
      });

      itemCard.addEventListener(`touchend`, function(e) {
        // Don't handle touch if user was scrolling
        if (isScrolling) {
          return;
        }

        e.preventDefault();
        handleCardNavigation.call(this, e);
      });

      // Add keyboard support
      itemCard.addEventListener(`keydown`, function(e) {
        if (e.key === `Enter` || e.key === ` `) {
          e.preventDefault();
          this.click();
        }
      });

      gameTrack.appendChild(itemCard);
    });

    // Update slider state
    totalItems = games.length;
    // Use setTimeout to ensure DOM is updated before calculating dimensions
    setTimeout(() => {
      updateCardWidth();
      rafThrottledUpdateSliderControls();
    }, 100);
  }
  

  
  // Slider functionality
  function updateSliderControls() {
    const maxVisibleCards = Math.floor(gameTrack.parentElement.offsetWidth / cardWidth);
    const maxIndex = Math.max(0, totalItems - maxVisibleCards);
    
    prevArrow.disabled = currentIndex <= 0;
    nextArrow.disabled = currentIndex >= maxIndex;
  }
  
  function slideToIndex(index) {
    const maxVisibleCards = Math.floor(gameTrack.parentElement.offsetWidth / cardWidth);
    const maxIndex = Math.max(0, totalItems - maxVisibleCards);

    currentIndex = Math.max(0, Math.min(index, maxIndex));
    const translateX = -currentIndex * cardWidth;

    gameTrack.style.transform = `translateX(${translateX}px)`;
    rafThrottledUpdateSliderControls();
  }
  
  function slideNext() {
    slideToIndex(currentIndex + 1);
  }
  
  function slidePrev() {
    slideToIndex(currentIndex - 1);
  }
  
  // Add event listeners for arrows
  prevArrow.addEventListener(`click`, slidePrev);
  nextArrow.addEventListener(`click`, slideNext);

  // Add touch support for arrows with scroll detection
  let prevArrowScrolling = false;
  let nextArrowScrolling = false;
  let prevTouchStartY = 0;
  let nextTouchStartY = 0;

  prevArrow.addEventListener(`touchstart`, (e) => {
    prevArrowScrolling = false;
    prevTouchStartY = e.touches[0].clientY;
  });

  prevArrow.addEventListener(`touchmove`, (e) => {
    const touchMoveY = e.touches[0].clientY;
    const deltaY = Math.abs(touchMoveY - prevTouchStartY);

    if (deltaY > 10) {
      prevArrowScrolling = true;
    }
  });

  prevArrow.addEventListener(`touchend`, (e) => {
    if (prevArrowScrolling) {
      return;
    }

    e.preventDefault();
    slidePrev();
  });

  nextArrow.addEventListener(`touchstart`, (e) => {
    nextArrowScrolling = false;
    nextTouchStartY = e.touches[0].clientY;
  });

  nextArrow.addEventListener(`touchmove`, (e) => {
    const touchMoveY = e.touches[0].clientY;
    const deltaY = Math.abs(touchMoveY - nextTouchStartY);

    if (deltaY > 10) {
      nextArrowScrolling = true;
    }
  });

  nextArrow.addEventListener(`touchend`, (e) => {
    if (nextArrowScrolling) {
      return;
    }

    e.preventDefault();
    slideNext();
  });
  
  // Add keyboard support for arrows
  prevArrow.addEventListener(`keydown`, function(e) {
    if (e.key === `Enter` || e.key === ` `) {
      e.preventDefault();
      slidePrev();
    }
  });
  
  nextArrow.addEventListener(`keydown`, function(e) {
    if (e.key === `Enter` || e.key === ` `) {
      e.preventDefault();
      slideNext();
    }
  });

  // Mouse drag event listeners
  gameTrack.addEventListener(`mousedown`, function(e) {
    e.preventDefault();
    startDrag(e.clientX);
  });

  document.addEventListener(`mousemove`, function(e) {
    if (isDragging) {
      e.preventDefault();
      updateDrag(e.clientX);
    }
  });

  document.addEventListener(`mouseup`, function(e) {
    if (isDragging) {
      e.preventDefault();
      endDrag();
    }
  });

  // Touch drag event listeners for mobile with gesture detection
  gameTrack.addEventListener(`touchstart`, function(e) {
    if (e.touches.length === 1) {
      // Initialize touch tracking
      touchStartX = e.touches[0].clientX;
      touchStartY = e.touches[0].clientY;
      gestureDetected = false;
      isVerticalGesture = false;
      touchStartedOnSlider = true;

      // Start as horizontal gesture by default - but don't prevent default yet
      // e.preventDefault(); // Don't block scroll on touchstart
      startDrag(e.touches[0].clientX);
    }
  }, { passive: false });

  // Gesture detection function
  function detectGestureDirection(currentX, currentY) {
    if (!gestureDetected) {
      const deltaX = Math.abs(currentX - touchStartX);
      const deltaY = Math.abs(currentY - touchStartY);

      // Check if movement exceeds threshold
      if (deltaX > GESTURE_THRESHOLD || deltaY > GESTURE_THRESHOLD) {
        gestureDetected = true;
        isVerticalGesture = deltaY > deltaX;

        // If vertical gesture detected, stop dragging and allow scrolling
        if (isVerticalGesture) {
          endDrag();
          touchStartedOnSlider = false; // Stop handling this touch sequence
          return true; // Indicate vertical gesture detected
        }
      }
    }
    return false; // No vertical gesture detected
  }

  sectionElement.addEventListener(`touchmove`, function(e) {
    // Only handle touch events that started on the slider
    if (e.touches.length === 1 && touchStartedOnSlider) {
      const currentX = e.touches[0].clientX;
      const currentY = e.touches[0].clientY;

      // Use gesture detection for better performance
      if (!gestureDetected) {
        const isVertical = detectGestureDirection(currentX, currentY);
        if (isVertical) {
          return; // Don't prevent default, allow vertical scrolling
        }
      }

      // If we're in horizontal drag mode (not vertical), continue dragging
      if (isDragging && !isVerticalGesture) {
        e.preventDefault();
        updateDrag(currentX);
      }
    }
  }, { passive: false });

  sectionElement.addEventListener(`touchend`, function(e) {
    // Only handle if touch started on slider
    if (touchStartedOnSlider) {
      if (isDragging && !isVerticalGesture) {
        e.preventDefault();
        endDrag();
      }

      // Reset gesture detection state
      gestureDetected = false;
      isVerticalGesture = false;
      touchStartedOnSlider = false;
    }
  }, { passive: false });
  
  // Update card width on resize
  function updateCardWidth() {
    const itemCard = gameTrack.querySelector(`.game-card, .provider-card`);
    if (itemCard) {
      const cardRect = itemCard.getBoundingClientRect();
      // Get gap from computed styles
      const trackStyles = window.getComputedStyle(gameTrack);
      const gap = parseInt(trackStyles.gap) || 16;
      cardWidth = cardRect.width + gap;
      slideToIndex(currentIndex); // Recalculate position
    }
  }
  
  // Mouse drag functionality
  function getCurrentTranslateX() {
    const transform = gameTrack.style.transform;
    const match = transform.match(/translateX\((-?\d+(?:\.\d+)?)px\)/);
    return match ? parseFloat(match[1]) : 0;
  }

  function startDrag(clientX) {
    isDragging = true;
    hasDragged = false;
    dragStartX = clientX;
    dragStartTranslateX = getCurrentTranslateX();
    lastDragX = clientX;
    dragVelocity = 0;
    dragStartTime = Date.now();

    // Disable transitions during drag
    gameTrack.style.transition = 'none';

    // Prevent text selection during drag
    document.body.style.userSelect = 'none';
    document.body.style.webkitUserSelect = 'none';
  }

  function updateDragInternal(clientX) {
    if (!isDragging) return;

    const deltaX = clientX - dragStartX;
    const newTranslateX = dragStartTranslateX + deltaX;

    // Mark as dragged if moved more than 5 pixels
    if (Math.abs(deltaX) > 5) {
      hasDragged = true;
    }

    // Calculate velocity for momentum
    const deltaTime = Date.now() - dragStartTime;
    if (deltaTime > 0) {
      dragVelocity = (clientX - lastDragX) / Math.max(deltaTime, 1) * 16; // 16ms frame time
    }
    lastDragX = clientX;

    // Apply drag with some resistance at boundaries
    const maxVisibleCards = Math.floor(gameTrack.parentElement.offsetWidth / cardWidth);
    const maxTranslateX = 0;
    const minTranslateX = -(Math.max(0, totalItems - maxVisibleCards) * cardWidth);

    let constrainedTranslateX = newTranslateX;

    // Add resistance at boundaries
    if (newTranslateX > maxTranslateX) {
      constrainedTranslateX = maxTranslateX + (newTranslateX - maxTranslateX) * 0.3;
    } else if (newTranslateX < minTranslateX) {
      constrainedTranslateX = minTranslateX + (newTranslateX - minTranslateX) * 0.3;
    }

    gameTrack.style.transform = `translateX(${constrainedTranslateX}px)`;
  }

  function updateDrag(clientX) {
    // Store the latest clientX for immediate velocity calculation
    if (isDragging) {
      const deltaTime = Date.now() - dragStartTime;
      if (deltaTime > 0) {
        dragVelocity = (clientX - lastDragX) / Math.max(deltaTime, 1) * 16;
      }
      lastDragX = clientX;
    }

    // Use rafThrottled version for actual DOM updates
    rafThrottledUpdateDrag(clientX);
  }

  function endDrag() {
    if (!isDragging) return;

    isDragging = false;

    // Re-enable transitions
    gameTrack.style.transition = '';

    // Re-enable text selection
    document.body.style.userSelect = '';
    document.body.style.webkitUserSelect = '';

    // Calculate final position with momentum
    const currentTranslateX = getCurrentTranslateX();
    const momentum = dragVelocity * 8; // Momentum multiplier
    const targetTranslateX = currentTranslateX + momentum;

    // Find the closest valid index
    const targetIndex = Math.round(-targetTranslateX / cardWidth);
    const maxVisibleCards = Math.floor(gameTrack.parentElement.offsetWidth / cardWidth);
    const maxIndex = Math.max(0, totalItems - maxVisibleCards);

    // Constrain to valid range
    const finalIndex = Math.max(0, Math.min(targetIndex, maxIndex));

    // Animate to final position
    slideToIndex(finalIndex);

    // Reset drag flag after a short delay to allow click events to check it
    setTimeout(() => {
      hasDragged = false;
    }, 10);

    // Reset gesture detection state
    gestureDetected = false;
    isVerticalGesture = false;
    touchStartedOnSlider = false;
  }

  // Handle window resize with debouncing
  window.addEventListener(`resize`, rafThrottledUpdateCardWidth);

  // Initialize with provided games
  if (games && games.length > 0) {
    renderGames();
  }

  // Public API
  return {
    renderGames,
    slideNext,
    slidePrev,
    slideToIndex
  };
};

// Common card creation functions
window.GameSliderUtils = {
  createGameCard: function(game, imageUrl, additionalElements = []) {
    const fallbackImageUrl = `data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzAwIiBoZWlnaHQ9IjIwMCIgdmlld0JveD0iMCAwIDMwMCAyMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIzMDAiIGhlaWdodD0iMjAwIiBmaWxsPSIjMmIyYjRmIi8+Cjx0ZXh0IHg9IjE1MCIgeT0iMTAwIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBmaWxsPSIjZmJkMTJkIiBmb250LWZhbWlseT0iQXJpYWwiIGZvbnQtc2l6ZT0iMTYiPkdhbWUgSW1hZ2U8L3RleHQ+Cjwvc3ZnPg==`;

    // Create main card container
    const gameCard = document.createElement(`div`);
    gameCard.className = `game-card`;
    gameCard.setAttribute(`data-game-id`, game.id);
    gameCard.setAttribute(`tabindex`, `0`);
    gameCard.setAttribute(`role`, `button`);

    // Create image element
    const gameImage = document.createElement(`img`);
    gameImage.src = imageUrl;
    gameImage.alt = game.name;
    gameImage.className = `game-image`;
    gameImage.addEventListener(`error`, function() {
      this.src = fallbackImageUrl;
    });

    // Create name element
    const gameName = document.createElement(`div`);
    gameName.className = `game-name`;
    gameName.textContent = game.name;

    // Assemble the card
    gameCard.appendChild(gameImage);
    
    // Add any additional elements (like live indicator)
    additionalElements.forEach(element => {
      gameCard.appendChild(element);
    });
    
    gameCard.appendChild(gameName);

    return gameCard;
  },

  createLiveIndicator: function() {
    const liveIndicator = document.createElement(`div`);
    liveIndicator.className = `live-indicator`;
    liveIndicator.textContent = `LIVE`;
    return liveIndicator;
  }
};
