/* Modern Game Slider Styles */

/* Section Base Styles */
.game-section {
  margin: 16px 0;
  position: relative;
}

.game-section-container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 24px;
  position: relative;
  z-index: 1;
}

.game-section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-bottom: 16px;
  border-bottom: 1px solid rgba(var(--primary-color), 0.1);
}

.game-section-title {
  font-size: 2.25rem;
  font-weight: 600;
  color: rgb(var(--primary-color));
  text-align: left;
  margin: 0;
  text-shadow: none;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  position: relative;
}

.game-section-title::after {
  content: '';
  position: absolute;
  bottom: -8px;
  left: 0;
  width: 60px;
  height: 3px;
  background: linear-gradient(90deg, rgb(var(--primary-color)) 0%, rgba(var(--primary-color), 0.3) 100%);
  border-radius: 2px;
}

/* Modern Slider Controls */
.slider-controls {
  display: flex;
  gap: 8px;
}

.slider-arrow {
  width: 44px;
  height: 44px;
  border-radius: 12px;
  border: none;
  background: rgba(var(--secondary-color), 0.8);
  color: rgba(255, 255, 255, 0.8);
  font-size: 1.1rem;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  display: flex;
  align-items: center;
  justify-content: center;
  backdrop-filter: blur(20px);
  user-select: none;
  box-shadow:
    0 4px 12px rgba(0, 0, 0, 0.15),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

.slider-arrow:hover {
  background: rgb(var(--primary-color));
  color: rgb(var(--secondary-color));
  transform: translateY(-1px) scale(1.05);
  box-shadow:
    0 8px 25px rgba(var(--primary-color), 0.25),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.slider-arrow:active {
  transform: translateY(0) scale(1.02);
}

.slider-arrow:disabled {
  opacity: 0.4;
  cursor: not-allowed;
  background: rgba(var(--secondary-color), 0.4);
  color: rgba(255, 255, 255, 0.4);
  box-shadow:
    0 2px 8px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.05);
}

.slider-arrow:disabled:hover {
  transform: none;
  background: rgba(var(--secondary-color), 0.4);
  color: rgba(255, 255, 255, 0.4);
  box-shadow:
    0 2px 8px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.05);
}

/* Modern Slider Container */
.game-section-slider {
  position: relative;
  overflow: hidden;
  border-radius: 20px;
  padding: 4px 0;
}

.game-section-track {
  display: flex;
  gap: 20px;
  transition: transform 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  padding: 16px 0 16px 4px;
  overflow: visible;
  cursor: grab;
  user-select: none;
  will-change: transform;
}

.game-section-track:active {
  cursor: grabbing;
}

/* Modern Game Cards */
.game-card {
  flex-shrink: 0;
  width: 240px;
  height: 280px;
  border-radius: 16px;
  overflow: hidden;
  cursor: pointer;
  /* transition: transform 0.4s cubic-bezier(0.4, 0, 0.2, 1); */
  /* border: 1px solid rgba(var(--primary-color), 0.1); */
  position: relative;
  /* background: linear-gradient(135deg, rgb(var(--secondary-color)) 0%, #1e1e3f 100%); */
  /* box-shadow:
    0 8px 32px rgba(0, 0, 0, 0.2),
    0 2px 8px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.05); */
}

/* .game-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg,
      rgba(var(--primary-color), 0.05) 0%,
      transparent 30%,
      rgba(var(--primary-color), 0.03) 70%,
      transparent 100%);
  z-index: 1;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  opacity: 0;
  border-radius: 16px;
}

.game-card::after {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  background: linear-gradient(135deg,
      rgba(var(--primary-color), 0.3) 0%,
      transparent 25%,
      rgba(var(--primary-color), 0.2) 50%,
      transparent 75%,
      rgba(var(--primary-color), 0.3) 100%);
  z-index: -1;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  opacity: 0;
  border-radius: 18px;
} */

.game-card:hover::before {
  opacity: 1;
}

.game-card:hover::after {
  opacity: 1;
}

/* .game-card:hover {
  transform: translateY(-8px) scale(1.02);
  border-color: rgba(var(--primary-color), 0.4);
} */

.game-section .game-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  object-position: top center;
  display: block;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  position: absolute;
  top: 0;
  left: 0;
  border-radius: 16px;
  user-select: none;
  -webkit-user-drag: none;
  -khtml-user-drag: none;
  -moz-user-drag: none;
  -o-user-drag: none;
  pointer-events: none;
}

.game-section .game-card:hover .game-image {
  transform: scale(1.08);
  filter: brightness(1.1) contrast(1.1);
}

.game-section .game-name {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  width: 100%;
  padding: 24px 20px 12px 20px;
  font-size: 1.1rem;
  font-weight: bold;
  color: rgba(255, 255, 255, 0.85);
  text-align: center;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  overflow: hidden;
  text-overflow: ellipsis;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  background: linear-gradient(to top,
      rgba(0, 0, 0, 0.95) 0%,
      rgba(0, 0, 0, 0.85) 50%,
      rgba(0, 0, 0, 0.6) 80%,
      transparent 100%);
  z-index: 3;
  border-radius: 0 0 16px 16px;
  min-height: 80px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.game-section .game-card:hover .game-name {
  color: rgb(var(--primary-color));
  background: linear-gradient(to top,
      rgba(0, 0, 0, 0.98) 0%,
      rgba(0, 0, 0, 0.90) 50%,
      rgba(0, 0, 0, 0.7) 80%,
      transparent 100%);
  /* text-shadow: 0 2px 8px rgba(var(--primary-color), 0.3); */
}

/* Loading Placeholders */
.game-section .loading-placeholder {
  animation: pulse 1.5s ease-in-out infinite;
  height: 280px;
}

.game-section .game-image-placeholder {
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.2) 50%, rgba(255, 255, 255, 0.1) 100%);
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
  position: absolute;
  top: 0;
  left: 0;
}

.game-section .game-section .game-name-placeholder {
  height: 20px;
  position: absolute;
  bottom: 16px;
  left: 16px;
  right: 16px;
  background: linear-gradient(90deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.2) 50%, rgba(255, 255, 255, 0.1) 100%);
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
  border-radius: 4px;
}

@keyframes pulse {

  0%,
  100% {
    opacity: 0.6;
  }

  50% {
    opacity: 1;
  }
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }

  100% {
    background-position: 200% 0;
  }
}

/* Error State */
.error-message {
  text-align: center;
  color: rgba(255, 255, 255, 0.7);
  font-size: 1.1rem;
  padding: 40px 20px;
}

.retry-button {
  background: linear-gradient(45deg, rgb(var(--primary-color)), rgb(var(--primary-color)));
  border: none;
  padding: 12px 24px;
  font-size: 1rem;
  font-weight: bold;
  color: rgb(var(--secondary-color));
  border-radius: 25px;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-top: 16px;
}

.retry-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(var(--primary-color), 0.3);
}

/* Modern Live Indicator (for Live Casino) */
.live-indicator {
  position: absolute;
  top: 16px;
  right: 16px;
  background: linear-gradient(135deg, #ff4444 0%, #ff6666 100%);
  color: white;
  padding: 6px 12px 6px 20px;
  border-radius: 20px;
  font-size: 0.75rem;
  font-weight: 700;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  z-index: 4;
  box-shadow:
    0 4px 12px rgba(255, 68, 68, 0.3),
    0 2px 4px rgba(0, 0, 0, 0.2);
  animation: livePulse 2s ease-in-out infinite;
  border: 1px solid rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  gap: 6px;
}

.live-indicator::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 10px;
  transform: translate(-50%, -50%);
  width: 6px;
  height: 6px;
  background: white;
  border-radius: 50%;
  animation: liveDot 1.5s ease-in-out infinite;
  flex-shrink: 0;
}

@keyframes livePulse {

  0%,
  100% {
    opacity: 1;
    transform: scale(1);
    box-shadow:
      0 4px 12px rgba(255, 68, 68, 0.3),
      0 2px 4px rgba(0, 0, 0, 0.2);
  }

  50% {
    opacity: 0.9;
    transform: scale(1.05);
    box-shadow:
      0 6px 20px rgba(255, 68, 68, 0.4),
      0 4px 8px rgba(0, 0, 0, 0.3);
  }
}

@keyframes liveDot {

  0%,
  100% {
    opacity: 1;
    transform: translateY(-50%) scale(1);
  }

  50% {
    opacity: 0.6;
    transform: translateY(-50%) scale(1.2);
  }
}



/* Responsive Design */
@media (max-width: 1024px) {
  .game-section {
    margin: 50px 0;
    padding: 0 0 32px 0;
  }

  .game-section-container {
    padding: 0 20px;
  }

  .game-section-header {
    margin-bottom: 28px;
  }

  .game-section-title {
    font-size: 2rem;
  }

  .slider-arrow {
    width: 40px;
    height: 40px;
    font-size: 1rem;
  }

  .game-section-track {
    gap: 18px;
    padding: 12px 0;
  }

  .game-card {
    width: 220px;
    height: 260px;
  }

  .game-section .game-name {
    font-size: 0.9rem;
    padding: 20px 18px 20px 18px;
    min-height: 70px;
  }

  .loading-placeholder {
    height: 260px;
  }

  .live-indicator {
    top: 14px;
    right: 14px;
    padding: 5px 10px;
    font-size: 0.7rem;
  }
}

@media (max-width: 768px) {
  .game-section {
    margin: 20px 0;
    padding: 0 0 12px 0;
  }

  .game-section-container {
    padding: 0 16px;
  }

  .game-section-header {
    margin-bottom: 20px;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
    gap: 12px;
    padding-bottom: 8px;
  }

  .game-section-title {
    font-size: 1.75rem;
  }

  .game-section-title::after {
    width: 50px;
    height: 2px;
  }

  .game-section-header .slider-controls {
    gap: 6px;
  }

  .slider-arrow {
    width: 38px;
    height: 38px;
    font-size: 0.95rem;
    border-radius: 10px;
  }

  .game-section-track {
    gap: 16px;
    padding: 10px 0;
  }

  .game-card {
    width: 180px;
    height: 220px;
    border-radius: 14px;
  }

  .game-image {
    border-radius: 14px;
  }

  .game-section .game-name {
    font-size: 0.85rem;
    padding: 18px 16px 18px 16px;
    border-radius: 0 0 14px 14px;
    min-height: 60px;
  }

  .loading-placeholder {
    height: 220px;
  }

  .live-indicator {
    top: 12px;
    right: 12px;
    padding: 4px 8px;
    font-size: 0.65rem;
    border-radius: 16px;
    animation: none;
  }
}

@media (max-width: 480px) {
  .game-section {
    margin: 15px 0;
    padding: 0 0 10px 0;
  }

  .game-section-container {
    padding: 0 10px;
  }

  .game-section-header {
    margin-bottom: 25px;
    gap: 15px;
  }

  .game-section-title {
    font-size: 1.4rem;
  }

  .slider-arrow {
    width: 36px;
    height: 36px;
    font-size: 0.9rem;
  }

  .game-section-track {
    gap: 12px;
  }

  .game-card {
    width: 160px;
    height: 200px;
  }

  .game-name {
    font-size: 0.8rem;
    padding: 16px 10px 16px 10px;
    min-height: 50px;
  }

  .loading-placeholder {
    height: 200px;
  }

  .live-indicator {
    top: 6px;
    right: 6px;
    padding: 2px 4px;
    font-size: 0.6rem;
  }
}

/* Extra small mobile devices */
@media (max-width: 360px) {
  .game-section-container {
    padding: 0 8px;
  }

  .game-section-header {
    flex-direction: column;
    align-items: center;
    text-align: center;
    gap: 15px;
  }

  .game-section-header .slider-controls {
    align-self: center;
  }

  .game-section-title {
    font-size: 1.2rem;
  }

  .game-card {
    width: 140px;
    height: 180px;
  }

  .game-name {
    font-size: 0.7rem;
    padding: 14px 8px 14px 8px;
    min-height: 45px;
  }

  .loading-placeholder {
    height: 180px;
  }

  .live-indicator {
    top: 4px;
    right: 4px;
    padding: 1px 3px;
    font-size: 0.55rem;
  }
}
