// Tournaments Section JavaScript
window.$LANDING.onLifecycle({ 
  selector: `.tournaments-section`, 
  onMount: (container, _, kill) => {
    
    // DOM Elements
    const tournamentsTrack = container.querySelector('#tournaments-track');
    const prevArrow = container.querySelector('#tournaments-prev-arrow');
    const nextArrow = container.querySelector('#tournaments-next-arrow');
    
    // State
    let tournaments = [];
    let games = [];
    let providers = [];
    let tournamentSlides = [];
    let currentSlide = 0;
    let isLoading = false;

    // Mouse drag state
    let isDragging = false;
    let dragStartX = 0;
    let dragStartTranslateX = 0;
    let lastDragX = 0;
    let dragVelocity = 0;
    let dragStartTime = 0;
    let hasDragged = false; // Track if user actually dragged (vs just clicked)

    // Touch gesture detection state
    let touchStartX = 0;
    let touchStartY = 0;
    let gestureDetected = false;
    let isVerticalGesture = false;
    let touchStartedOnSlider = false; // Track if touch started on slider
    const GESTURE_THRESHOLD = 10; // Minimum movement to detect gesture direction
    
    // API URLs
    const TOURNAMENTS_API = 'https://pn17.pfnow.net/api/tr/tournaments';
    const GAMES_API = `${window.origin}/odin/api/user/casinoapi/getReservedGames`;
    const PROVIDERS_API = `${window.origin}/odin/api/user/casinoapi/getReservedVendors/ordered`;
    const TOURNAMENT_SLIDER_API = `https://pn17.pfnow.net/api/tr/consumer`;
    const TOURNAMENT_SLIDER_TYPE = 37;
    const SLIDER_DEVICE_TYPE = window.origin.includes('//m.') ? 2 : 1;
    const IMAGE_BASE_URL = `https://cdn.pfcdn100.com/merchants/pn17/uploads/`;

    // Cache keys for providers (same as in providers.js)
    const PROVIDERS_CACHE_KEY = `$landing_providers`;
    const PROVIDERS_CACHE_TIMESTAMP_KEY = `$landing_providers_timestamp`;
    const PROVIDERS_CACHE_DURATION = 3 * 60 * 60 * 1000; // 3 hours
    
    function rafThrottle(fn) {
      let locked = false;
      return (...args) => {
        if (locked) return;
        locked = true;
        requestAnimationFrame(() => {
          fn(...args);
          locked = false;
        });
      };
    }
    
    // Function to fetch games (no caching)
    async function getGames() {
      try {
        const response = await fetch(GAMES_API, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            requestBody: {
              currencyId: 1,
              gameType: `casino`
            },
          })
        });

        if (!response.ok) throw new Error('Failed to fetch games');

        const data = await response.json();

        return data.data.games;
      } catch (error) {
        console.error('Error fetching games:', error);
        return [];
      }
    }

    // Function to fetch providers (with caching, same as providers.js)
    async function getProviders() {
      try {
        // Check cache first
        const cachedData = localStorage.getItem(PROVIDERS_CACHE_KEY);
        const cachedTimestamp = localStorage.getItem(PROVIDERS_CACHE_TIMESTAMP_KEY);
        const now = Date.now();

        if (cachedData && cachedTimestamp && (now - parseInt(cachedTimestamp)) < PROVIDERS_CACHE_DURATION) {
          return JSON.parse(cachedData);
        }

        // Fetch from API
        const response = await fetch(PROVIDERS_API, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            requestBody: {
              currencyId: 1
            },
            languageId: 1,
            device: 'd'
          })
        });

        if (!response.ok) throw new Error('Failed to fetch providers');

        const data = await response.json();

        if (data.success && data.data && data.data.vendors) {
          const providersData = data.data.vendors;

          // Cache the data
          localStorage.setItem(PROVIDERS_CACHE_KEY, JSON.stringify(providersData));
          localStorage.setItem(PROVIDERS_CACHE_TIMESTAMP_KEY, now.toString());

          return providersData;
        } else {
          throw new Error('Invalid providers response format');
        }
      } catch (error) {
        console.error('Error fetching providers:', error);
        return [];
      }
    }

    // Function to get provider name by vendor ID
    function getProviderName(vendorId) {
      const provider = providers.find(p => p.id === vendorId);
      return provider ? provider.name : 'Unknown';
    }

    // Function to parse custom_class string
    function parseCustomClass(customClass) {
      if (!customClass) return {};

      const result = {};
      const pairs = customClass.split(';');

      pairs.forEach(pair => {
        const [key, value] = pair.split('=');
        if (key && value) {
          result[key.trim()] = value.trim();
        }
      });

      return result;
    }

    // Function to fetch tournament slides
    async function fetchTournamentSlides() {
      try {
        console.log(`API'den turnuva slaytları alınıyor...`);

        const response = await fetch(TOURNAMENT_SLIDER_API, {
          method: `GET`,
          headers: {
            [`Content-Type`]: `application/json`,
          }
        });

        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }

        const data = await response.json();

        if (!data.sliders || !Array.isArray(data.sliders)) {
          throw new Error(`Invalid response format`);
        }

        return data.sliders.filter(s => s.type === TOURNAMENT_SLIDER_TYPE && s.m_t === SLIDER_DEVICE_TYPE);
      } catch (error) {
        console.error(`Turnuva slaytları alınırken hata:`, error);
        return [];
      }
    }

    // Function to get eligible games from tournament settings
    function getEligibleGames(tournamentSettings, gamesList) {
      if (!tournamentSettings || !tournamentSettings[0] || !gamesList) {
        return [];
      }

      // Arrays to store different types of filters
      const excludedGameIds = [];
      const excludedVendorIds = [];
      const excludedCategoryIds = [];
      const includedGameIds = [];
      const includedVendorIds = [];
      const includedCategoryIds = [];
      const customFilters = [];

      // Process each setting in the tournament
      for (const settingGroup of tournamentSettings) {
        for (const setting of settingGroup) {
          // Skip maintype 5 (special handling)
          if (setting.maintype === 5) {
            customFilters.push(setting.c_u);
            continue;
          }

          // Process units for each setting
          for (const unit of setting.units || []) {
            const gameId = unit.unit;

            // Determine if this is an inclusion (cn=4) or exclusion rule
            const isInclusion = setting.cn === 4;

            // Categorize by maintype
            switch (setting.maintype) {
              case 4: // Game ID filter
                if (isInclusion) {
                  includedGameIds.push(gameId);
                } else {
                  excludedGameIds.push(gameId);
                }
                break;

              case 3: // Vendor ID filter
                if (isInclusion) {
                  includedVendorIds.push(gameId);
                } else {
                  excludedVendorIds.push(gameId);
                }
                break;

              case 6: // Category ID filter
                if (isInclusion) {
                  includedCategoryIds.push(gameId);
                } else {
                  excludedCategoryIds.push(gameId);
                }
                break;
            }
          }
        }
      }

      // Filter games based on the collected criteria
      const eligibleGames = gamesList.filter(game => {
        // Check exclusions first (if excluded, game is not eligible)
        if (excludedGameIds.includes(game.id) ||
            excludedVendorIds.includes(game.vendorId) ||
            excludedCategoryIds.includes(game.categoryId)) {
          return false;
        }

        // Check inclusions (if any inclusion rules exist, game must match at least one)
        const hasInclusionRules = includedGameIds.length > 0 ||
                                 includedVendorIds.length > 0 ||
                                 includedCategoryIds.length > 0;

        if (hasInclusionRules) {
          const matchesInclusion = includedGameIds.includes(game.id) ||
                                  includedVendorIds.includes(game.vendorId) ||
                                  includedCategoryIds.includes(game.categoryId);

          if (!matchesInclusion) {
            return false;
          }
        }

        // Apply custom filters (simplified version)
        if (customFilters.length > 0) {
          // Custom filter logic would go here
          // For now, we'll assume all games pass custom filters
        }

        // Game must be open/available
        return game.open !== false;
      });

      // Sort by popularity and order, return all eligible games
      return eligibleGames
        .sort((a, b) => {
          // Sort by orderBy first (ascending), then by popular (descending)
          if (a.orderBy !== b.orderBy) {
            return (a.orderBy || 999) - (b.orderBy || 999);
          }
          return (b.popular || 0) - (a.popular || 0);
        });
    }
    
    // Function to determine tournament status
    function getTournamentStatus(startDate, endDate) {
      const now = Math.floor(Date.now() / 1000);
      if (now < startDate) return 'soon';
      if (now >= startDate && now < endDate) return 'ongoing';
      return 'ended';
    }

    // Function to format countdown
    function formatCountdown(startDate, endDate) {
      const now = Math.floor(Date.now() / 1000);

      // If tournament hasn't started yet, count down to start
      if (now < startDate) {
        const diff = startDate - now;
        const days = Math.floor(diff / (24 * 60 * 60));
        const hours = Math.floor((diff % (24 * 60 * 60)) / (60 * 60));
        const minutes = Math.floor((diff % (60 * 60)) / 60);
        const seconds = diff % 60;

        return {
          status: 'countdown',
          statusText: 'Başlangıç',
          hasCountdown: true,
          days,
          hours,
          minutes,
          seconds
        };
      }

      // If tournament is ongoing, count down to end
      if (now >= startDate && now < endDate) {
        const diff = endDate - now;
        const days = Math.floor(diff / (24 * 60 * 60));
        const hours = Math.floor((diff % (24 * 60 * 60)) / (60 * 60));
        const minutes = Math.floor((diff % (60 * 60)) / 60);
        const seconds = diff % 60;

        return {
          status: 'ongoing',
          statusText: 'Devam Ediyor',
          hasCountdown: true,
          days,
          hours,
          minutes,
          seconds
        };
      }

      // Tournament has ended
      return {
        status: 'ended',
        statusText: 'Sona Erdi',
        hasCountdown: false,
        days: 0,
        hours: 0,
        minutes: 0,
        seconds: 0
      };
    }

    // Function to update countdowns
    function updateCountdowns() {
      const countdownContainers = document.querySelectorAll('.tournament-countdown-container[data-start-date][data-end-date]');
      countdownContainers.forEach(container => {
        const startDate = parseInt(container.getAttribute('data-start-date'));
        const endDate = parseInt(container.getAttribute('data-end-date'));
        const countdown = formatCountdown(startDate, endDate);

        // Update individual countdown values
        const daysElement = container.querySelector('[data-unit="days"]');
        const hoursElement = container.querySelector('[data-unit="hours"]');
        const minutesElement = container.querySelector('[data-unit="minutes"]');
        const secondsElement = container.querySelector('[data-unit="seconds"]');

        if (daysElement) daysElement.textContent = countdown.days.toString().padStart(2, '0');
        if (hoursElement) hoursElement.textContent = countdown.hours.toString().padStart(2, '0');
        if (minutesElement) minutesElement.textContent = countdown.minutes.toString().padStart(2, '0');
        if (secondsElement) secondsElement.textContent = countdown.seconds.toString().padStart(2, '0');
      });

      // Update status elements
      const statusElements = document.querySelectorAll('.tournament-status');
      statusElements.forEach(statusElement => {
        const container = statusElement.closest('.tournament-status-container');
        const countdownContainer = container?.querySelector('.tournament-countdown-container');

        if (countdownContainer) {
          const startDate = parseInt(countdownContainer.getAttribute('data-start-date'));
          const endDate = parseInt(countdownContainer.getAttribute('data-end-date'));
          const countdown = formatCountdown(startDate, endDate);

          statusElement.textContent = countdown.statusText;
          statusElement.className = `tournament-status ${countdown.status}`;
        }
      });
    }


    // Function to setup shadow root for image text
    function setupImageTextShadowRoot(card, tournament) {
      const textContainer = card.querySelector('.tournament-image-text-container');
      if (!textContainer) return;

      const content = tournament.content || tournament.content_b || tournament.content_c
      if (!content || content.trim() === '') return;

      // Create shadow root
      const shadowRoot = textContainer.attachShadow({ mode: 'open' });

      // Create styles for the shadow DOM
      const style = document.createElement('style');
      style.textContent = `
        :host {
          position: absolute;
          left: 20px;
          z-index: 2;
        }

        .tournament-text {
          color: white;
          font-size: 1.1rem;
          font-weight: 600;
          line-height: 1.4;
          text-shadow: 0 2px 8px rgba(0, 0, 0, 0.5);
          margin: 0;
          padding: 0;
          padding: 12px 0;
          max-width: 50%;
        }

        @media screen and (max-width: 768px) {
          .tournament-text {
            display: none;
          }
        }

        /* Markdown elements styling */
        .tournament-text strong,
        .tournament-text b {
          color: rgb(251, 209, 45);
          font-weight: 700;
          text-shadow: 0 1px 3px rgba(0, 0, 0, 0.7);
        }

        .tournament-text em,
        .tournament-text i {
          color: rgba(251, 209, 45, 0.9);
          font-style: italic;
          font-weight: 500;
        }

        .tournament-text br {
          line-height: 1.6;
        }

        .tournament-text p {
          margin: 0 0 8px 0;
          padding: 0;
        }

        .tournament-text p:last-child {
          margin-bottom: 0;
        }

        .tournament-text span {
          color: inherit;
        }

        .tournament-text small {
          font-size: 0.9em;
          color: rgba(255, 255, 255, 0.8);
          font-weight: 500;
        }

        .tournament-text mark {
          background: linear-gradient(135deg, rgba(251, 209, 45, 0.3), rgba(251, 209, 45, 0.2));
          color: rgb(251, 209, 45);
          padding: 2px 4px;
          border-radius: 3px;
          font-weight: 600;
        }

        .tournament-text code {
          background: rgba(251, 209, 45, 0.1);
          color: rgb(251, 209, 45);
          padding: 2px 6px;
          border-radius: 4px;
          font-family: 'Courier New', monospace;
          font-size: 0.9em;
          font-weight: 500;
        }

        .tournament-text a {
          color: rgb(251, 209, 45);
          text-decoration: none;
          font-weight: 600;
          transition: all 0.2s ease;
        }

        .tournament-text a:hover {
          color: rgba(251, 209, 45, 0.8);
          text-shadow: 0 0 8px rgba(251, 209, 45, 0.4);
        }

        @media (max-width: 768px) {
          :host {
            left: 12px;
            max-width: 70%;
          }

          .tournament-text {
            font-size: 1rem;
            line-height: 1.3;
          }

          .tournament-text strong,
          .tournament-text b {
            font-weight: 600;
          }

          .tournament-text small {
            font-size: 0.85em;
          }

          .tournament-text mark,
          .tournament-text code {
            padding: 1px 3px;
            font-size: 0.85em;
          }
        }
      `;

      // Create text element
      const textElement = document.createElement('div');
      textElement.className = 'tournament-text';

      // Use sanitized HTML content to support markdown elements safely
      textElement.innerHTML = content.replaceAll('<br />', '<br /><br />');

      // Append styles and text to shadow root
      shadowRoot.appendChild(style);
      shadowRoot.appendChild(textElement);
    }

    // Function to setup event listeners for tournament card
    function setupCardEventListeners(card) {
      // Tab switching
      const tabs = card.querySelectorAll('.tournament-tab');
      const panels = card.querySelectorAll('.tournament-tab-panel');

      tabs.forEach(tab => {
        let isScrolling = false;
        let touchStartY = 0;

        const handleTabSwitch = (e) => {
          // Prevent tab switching if user was dragging
          if (hasDragged) {
            e.preventDefault();
            return;
          }

          const targetTab = tab.getAttribute('data-tab');

          // Remove active class from all tabs and panels in this card
          tabs.forEach(t => t.classList.remove('active'));
          panels.forEach(p => p.classList.remove('active'));

          // Add active class to clicked tab and corresponding panel
          tab.classList.add('active');
          const targetPanel = card.querySelector(`[data-panel="${targetTab}"]`);
          if (targetPanel) {
            targetPanel.classList.add('active');
          }
        };
        const rafThrottledHandleTabSwitch = rafThrottle(handleTabSwitch);

        tab.addEventListener('click', rafThrottledHandleTabSwitch);

        // Add touch support with scroll detection
        tab.addEventListener('touchstart', (e) => {
          isScrolling = false;
          touchStartY = e.touches[0].clientY;
        });

        tab.addEventListener('touchmove', (e) => {
          const touchMoveY = e.touches[0].clientY;
          const deltaY = Math.abs(touchMoveY - touchStartY);

          if (deltaY > 10) {
            isScrolling = true;
          }
        });

        tab.addEventListener('touchend', (e) => {
          if (isScrolling) {
            return;
          }

          e.preventDefault();
          rafThrottledHandleTabSwitch(e);
        });
      });

      // Game play buttons
      const playButtons = card.querySelectorAll('.tournament-game-play-btn');
      playButtons.forEach(button => {
        let isScrolling = false;
        let touchStartY = 0;

        const handleGamePlay = (e) => {
          // Prevent navigation if user was dragging
          if (hasDragged) {
            e.preventDefault();
            return;
          }

          const gameItem = button.closest('.tournament-game-item');
          const gameId = gameItem.getAttribute('data-game-id');
          if (gameId) {
            $LANDING.navigate(`/games/casino/detail/demo/${gameId}`);
          }
        };
        const rafThrottledHandleGamePlay = rafThrottle(handleGamePlay);

        button.addEventListener('click', rafThrottledHandleGamePlay);

        // Add touch support with scroll detection
        button.addEventListener('touchstart', (e) => {
          isScrolling = false;
          touchStartY = e.touches[0].clientY;
        });

        button.addEventListener('touchmove', (e) => {
          const touchMoveY = e.touches[0].clientY;
          const deltaY = Math.abs(touchMoveY - touchStartY);

          if (deltaY > 10) {
            isScrolling = true;
          }
        });

        button.addEventListener('touchend', (e) => {
          if (isScrolling) {
            return;
          }

          e.preventDefault();
          rafThrottledHandleGamePlay(e);
        });
      });

      // Action buttons
      const participateBtn = card.querySelector('[data-action="participate"]');
      const detailsBtn = card.querySelector('[data-action="details"]');

      if (participateBtn) {
        let participateScrolling = false;
        let participateTouchStartY = 0;

        const handleParticipate = (e) => {
          // Prevent navigation if user was dragging
          if (hasDragged) {
            e.preventDefault();
            return;
          }

          $LANDING.navigate('/authentication/signup');
        };
        const rafThrottledHandleParticipate = rafThrottle(handleParticipate);

        participateBtn.addEventListener('click', rafThrottledHandleParticipate);

        // Add touch support with scroll detection
        participateBtn.addEventListener('touchstart', (e) => {
          participateScrolling = false;
          participateTouchStartY = e.touches[0].clientY;
        });

        participateBtn.addEventListener('touchmove', (e) => {
          const touchMoveY = e.touches[0].clientY;
          const deltaY = Math.abs(touchMoveY - participateTouchStartY);

          if (deltaY > 10) {
            participateScrolling = true;
          }
        });

        participateBtn.addEventListener('touchend', (e) => {
          if (participateScrolling) {
            return;
          }

          e.preventDefault();
          rafThrottledHandleParticipate(e);
        });
      }

      if (detailsBtn) {
        let detailsScrolling = false;
        let detailsTouchStartY = 0;

        const handleDetails = (e) => {
          // Prevent navigation if user was dragging
          if (hasDragged) {
            e.preventDefault();
            return;
          }

          $LANDING.navigate('/pages/tournaments');
        };
        const rafThrottledHandleDetails = rafThrottle(handleDetails);

        detailsBtn.addEventListener('click', rafThrottledHandleDetails);

        // Add touch support with scroll detection
        detailsBtn.addEventListener('touchstart', (e) => {
          detailsScrolling = false;
          detailsTouchStartY = e.touches[0].clientY;
        });

        detailsBtn.addEventListener('touchmove', (e) => {
          const touchMoveY = e.touches[0].clientY;
          const deltaY = Math.abs(touchMoveY - detailsTouchStartY);

          if (deltaY > 10) {
            detailsScrolling = true;
          }
        });

        detailsBtn.addEventListener('touchend', (e) => {
          if (detailsScrolling) {
            return;
          }

          e.preventDefault();
          rafThrottledHandleDetails(e);
        });
      }
    }
    
    // Function to create tournament card
    function createTournamentCard(tournament) {
      const countdown = formatCountdown(tournament.start_date, tournament.end_date);
      const imageUrl = tournament.image ||
        'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAwIiBoZWlnaHQ9IjIwMCIgdmlld0JveD0iMCAwIDQwMCAyMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSI0MDAiIGhlaWdodD0iMjAwIiBmaWxsPSIjMmIyYjRmIi8+Cjx0ZXh0IHg9IjIwMCIgeT0iMTAwIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBmaWxsPSIjZmJkMTJkIiBmb250LWZhbWlseT0iQXJpYWwiIGZvbnQtc2l6ZT0iMjAiPlR1cm5hdmE8L3RleHQ+Cjwvc3ZnPg==';

      const card = document.createElement('div');
      card.className = 'tournament-card';
      
      // Get eligible games using the new logic
      const eligibleGameObjects = getEligibleGames(tournament.settings, games);
      
      // Create leaderboard HTML with 4 columns
      let leaderboardHtml = '';
      const leaderboardItems = [];

      // Get top 3 participants or create empty rows
      for (let i = 0; i < 3; i++) {
        const participant = tournament.partipicants && tournament.partipicants[i];
        const reward = tournament.rewards && tournament.rewards[i];

        if (participant) {
          leaderboardItems.push(`
            <div class="leaderboard-row">
              <div class="leaderboard-position">${i + 1}</div>
              <div class="leaderboard-username">${participant.nickname}</div>
              <div class="leaderboard-points">${participant.point || 0}</div>
              <div class="leaderboard-prize">${reward ? reward.value : '-'}</div>
            </div>
          `);
        } else {
          leaderboardItems.push(`
            <div class="leaderboard-row leaderboard-row-empty">
              <div class="leaderboard-position">${i + 1}</div>
              <div class="leaderboard-username">-</div>
              <div class="leaderboard-points">-</div>
              <div class="leaderboard-prize">-</div>
            </div>
          `);
        }
      }

      leaderboardHtml = `
        <div class="tournament-leaderboard">
          <div class="leaderboard-header">
            <div class="leaderboard-header-position">Sıra</div>
            <div class="leaderboard-header-username">Kullanıcı</div>
            <div class="leaderboard-header-points">Puan</div>
            <div class="leaderboard-header-prize">Ödül</div>
          </div>
          <div class="leaderboard-body">
            ${leaderboardItems.join('')}
          </div>
        </div>
      `;
      
      card.innerHTML = `
        <div class="tournament-card-inner">
          <div class="tournament-info-column">
            <h3 class="tournament-name">${tournament.name}</h3>

            <div class="tournament-status-container">
              <div class="tournament-status ${countdown.status}">
                ${countdown.statusText}
              </div>
              ${countdown.hasCountdown ? `
                <div class="tournament-countdown-container" data-start-date="${tournament.start_date}" data-end-date="${tournament.end_date}">
                  <div class="countdown-unit">
                    <span class="countdown-value" data-unit="days">${countdown.days.toString().padStart(2, '0')}</span>
                    <span class="countdown-label">Gün</span>
                  </div>
                  <div class="countdown-separator">:</div>
                  <div class="countdown-unit">
                    <span class="countdown-value" data-unit="hours">${countdown.hours.toString().padStart(2, '0')}</span>
                    <span class="countdown-label">Saat</span>
                  </div>
                  <div class="countdown-separator">:</div>
                  <div class="countdown-unit">
                    <span class="countdown-value" data-unit="minutes">${countdown.minutes.toString().padStart(2, '0')}</span>
                    <span class="countdown-label">Dakika</span>
                  </div>
                  <div class="countdown-separator">:</div>
                  <div class="countdown-unit">
                    <span class="countdown-value" data-unit="seconds">${countdown.seconds.toString().padStart(2, '0')}</span>
                    <span class="countdown-label">Saniye</span>
                  </div>
                </div>
              ` : ''}
            </div>

            <div class="tournament-prize-pool">
              <span class="prize-pool-label">Toplam Ödül Havuzu</span>
              <span class="prize-pool-amount">${tournament.p_pool}</span>
            </div>
            <div class="tournament-tabs-container">
              <div class="tournament-tabs">
                <button class="tournament-tab active" data-tab="leaderboard">
                  <span class="tournament-tab-label">Liderlik Tablosu</span>
                </button>
                <button class="tournament-tab" data-tab="games">
                  <span class="tournament-tab-label">Oyunlar (${eligibleGameObjects.length})</span>
                </button>
              </div>
              <div class="tournament-tab-content">
                <div class="tournament-tab-panel active" data-panel="leaderboard">
                  <div class="tournament-tab-panel-inner">
                    ${leaderboardHtml}
                  </div>
                </div>
                <div class="tournament-tab-panel" data-panel="games">
                  <div class="tournament-tab-panel-inner">
                    ${eligibleGameObjects.length > 0 ? `
                      <div class="tournament-games-list">
                        ${eligibleGameObjects.slice(0, 10).map((game, index) => `
                          <div class="tournament-game-item" data-game-id="${game.id}">
                            <div class="tournament-game-rank">${index + 1}</div>
                            <div class="tournament-game-image-container">
                              <img src="//v3.pro1staticserv.com/common/assets/images/casino/300x200/${game.id}.jpg"
                                   alt="${game.name}"
                                   class="tournament-game-image"
                                   onerror="this.src='data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iMzAiIHZpZXdCb3g9IjAgMCA0MCAzMCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjQwIiBoZWlnaHQ9IjMwIiBmaWxsPSIjMmIyYjRmIiByeD0iNCIvPgo8dGV4dCB4PSIyMCIgeT0iMTgiIHRleHQtYW5jaG9yPSJtaWRkbGUiIGZpbGw9IiNmYmQxMmQiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSI4Ij5HYW1lPC90ZXh0Pgo8L3N2Zz4='">
                            </div>
                            <div class="tournament-game-info">
                              <div class="tournament-game-name">${game.name}</div>
                              <div class="tournament-game-meta">
                                <span class="tournament-game-provider">${getProviderName(game.vendorId)}</span>
                                <span class="tournament-game-dot">•</span>
                                <span class="tournament-game-status">
                                  <span class="tournament-game-status-dot"></span>
                                  Aktif
                                </span>
                              </div>
                            </div>
                            <div class="tournament-game-actions">
                              <button class="tournament-game-play-btn" title="Oyunu Oyna">
                                <span>▶</span>
                              </button>
                            </div>
                          </div>
                        `).join('')}
                      </div>
                    ` : `
                      <div class="tournament-empty-games">
                        <div class="tournament-empty-icon">🎮</div>
                        <div class="tournament-empty-content">
                          <h4>Oyun Bulunamadı</h4>
                          <p>Bu turnuva için uygun oyun bulunmuyor.</p>
                          <span>Lütfen daha sonra tekrar kontrol edin.</span>
                        </div>
                      </div>
                    `}
                  </div>
                </div>
              </div>
            </div>

            <div class="tournament-actions">
              <button class="tournament-btn tournament-btn-primary" data-action="participate">
                🎮 Katıl
              </button>
              <button class="tournament-btn tournament-btn-secondary" data-action="details">
                📋 Detaylar
              </button>
            </div>
          </div>
          <div class="tournament-image-column">
            <img src="${imageUrl}" alt="${tournament.name}" class="tournament-image">
            <div class="tournament-image-text-container"></div>
          </div>
        </div>
      `;

      // Add event listeners for tabs and buttons
      setupCardEventListeners(card);

      // Setup shadow root for image text
      setupImageTextShadowRoot(card, tournament);

      return card;
    }
    
    // Function to render tournaments
    function renderTournaments() {
      if (!tournaments || tournaments.length === 0) {
        showErrorState();
        return;
      }
      
      tournamentsTrack.innerHTML = '';
      
      tournaments.forEach(tournament => {
        const card = createTournamentCard(tournament);
        tournamentsTrack.appendChild(card);
      });
      
      updateSliderControls();
    }
    
    // Function to show error state
    function showErrorState() {
      tournamentsTrack.innerHTML = `
        <div class="tournament-card" style="flex: 1; display: flex; align-items: center; justify-content: center; min-height: 300px;">
          <div style="text-align: center; color: rgba(255, 255, 255, 0.6);">
            <p>Turnuvalar yüklenirken bir hata oluştu.</p>
            <p style="font-size: 0.875rem; margin-top: 8px;">Lütfen daha sonra tekrar deneyin.</p>
          </div>
        </div>
      `;
    }
    
    // Function to update slider controls
    function updateSliderControls() {
      const maxSlides = Math.max(0, tournaments.length - 1);

      prevArrow.disabled = currentSlide <= 0;
      nextArrow.disabled = currentSlide >= maxSlides;

      prevArrow.style.opacity = prevArrow.disabled ? '0.5' : '1';
      nextArrow.style.opacity = nextArrow.disabled ? '0.5' : '1';
    }

    // Function to slide tournaments
    function slideTournaments(direction) {
      const maxSlides = Math.max(0, tournaments.length - 1);

      if (direction === 'next' && currentSlide < maxSlides) {
        currentSlide++;
      } else if (direction === 'prev' && currentSlide > 0) {
        currentSlide--;
      }

      // Each slide is 100% width
      const translateX = -currentSlide * 100;
      tournamentsTrack.style.transform = `translateX(${translateX}%)`;

      updateSliderControls();
    }

    // Function to slide to specific index
    function slideToIndex(index) {
      const maxSlides = Math.max(0, tournaments.length - 1);
      currentSlide = Math.max(0, Math.min(index, maxSlides));

      const translateX = -currentSlide * 100;
      tournamentsTrack.style.transform = `translateX(${translateX}%)`;

      updateSliderControls();
    }

    // Mouse drag functionality
    function getCurrentTranslateX() {
      const transform = tournamentsTrack.style.transform;
      const match = transform.match(/translateX\((-?\d+(?:\.\d+)?)%\)/);
      return match ? parseFloat(match[1]) : 0;
    }

    function startDrag(clientX) {
      isDragging = true;
      hasDragged = false;
      dragStartX = clientX;
      dragStartTranslateX = getCurrentTranslateX();
      lastDragX = clientX;
      dragVelocity = 0;
      dragStartTime = Date.now();

      // Disable transitions during drag
      tournamentsTrack.style.transition = 'none';

      // Prevent text selection during drag
      document.body.style.userSelect = 'none';
      document.body.style.webkitUserSelect = 'none';
    }

    function updateDrag(clientX) {
      if (!isDragging) return;

      const containerWidth = tournamentsTrack.parentElement.offsetWidth;
      const deltaX = clientX - dragStartX;
      const deltaPercent = (deltaX / containerWidth) * 100;
      const newTranslateX = dragStartTranslateX + deltaPercent;

      // Mark as dragged if moved more than 5 pixels
      if (Math.abs(deltaX) > 5) {
        hasDragged = true;
      }

      // Calculate velocity for momentum
      const deltaTime = Date.now() - dragStartTime;
      if (deltaTime > 0) {
        dragVelocity = (clientX - lastDragX) / Math.max(deltaTime, 1) * 16; // 16ms frame time
      }
      lastDragX = clientX;

      // Apply drag with some resistance at boundaries
      const maxTranslateX = 0;
      const minTranslateX = -(Math.max(0, tournaments.length - 1) * 100);

      let constrainedTranslateX = newTranslateX;
      if (newTranslateX > maxTranslateX) {
        constrainedTranslateX = maxTranslateX + (newTranslateX - maxTranslateX) * 0.3;
      } else if (newTranslateX < minTranslateX) {
        constrainedTranslateX = minTranslateX + (newTranslateX - minTranslateX) * 0.3;
      }

      tournamentsTrack.style.transform = `translateX(${constrainedTranslateX}%)`;
    }

    function endDrag() {
      if (!isDragging) return;

      isDragging = false;

      // Re-enable transitions
      tournamentsTrack.style.transition = '';

      // Re-enable text selection
      document.body.style.userSelect = '';
      document.body.style.webkitUserSelect = '';

      // Calculate final position with momentum
      const currentTranslateX = getCurrentTranslateX();
      const containerWidth = tournamentsTrack.parentElement.offsetWidth;
      const momentum = (dragVelocity * 8 / containerWidth) * 100; // Convert to percentage
      const targetTranslateX = currentTranslateX + momentum;

      // Find the closest valid slide
      const targetIndex = Math.round(-targetTranslateX / 100);
      const maxSlides = Math.max(0, tournaments.length - 1);
      const finalIndex = Math.max(0, Math.min(targetIndex, maxSlides));

      // Animate to final position
      slideToIndex(finalIndex);

      // Reset drag flag after a short delay to allow click events to check it
      setTimeout(() => {
        hasDragged = false;
      }, 10);

      // Reset gesture detection state
      gestureDetected = false;
      isVerticalGesture = false;
      touchStartedOnSlider = false;
    }
    
    // Function to load tournaments
    async function loadTournaments() {
      if (isLoading) return;

      isLoading = true;

      try {
        // Load providers, games, and tournament slides in parallel
        const [providersData, gamesData, slidesData] = await Promise.all([
          getProviders(),
          getGames(),
          fetchTournamentSlides()
        ]);

        providers = providersData;
        games = gamesData;
        tournamentSlides = slidesData;

        console.log(`${tournamentSlides.length} turnuva slaytı yüklendi`);

        // Load tournaments
        const response = await fetch(TOURNAMENTS_API);
        if (!response.ok) throw new Error('Failed to fetch tournaments');

        const data = await response.json();

        tournaments = tournamentSlides.map(slide => {
          const customData = parseCustomClass(slide.custom_class);

          const tournamentData = data.find(t =>
            t.id.toString() === customData.id && t.end_date > Math.floor(Date.now() / 1000)
          );
          if (!tournamentData) return null;

          return {
            ...tournamentData,
            image: slide.path.startsWith('http') ? slide.path : IMAGE_BASE_URL + slide.path,
          };
        }).filter(Boolean);

        renderTournaments();

      } catch (error) {
        console.error('Error loading tournaments:', error);
        showErrorState();
      } finally {
        isLoading = false;
      }
    }
    
    // Event listeners
    prevArrow.addEventListener('click', () => slideTournaments('prev'));
    nextArrow.addEventListener('click', () => slideTournaments('next'));

    // Add touch support for arrows with scroll detection
    let prevArrowScrolling = false;
    let nextArrowScrolling = false;
    let prevTouchStartY = 0;
    let nextTouchStartY = 0;

    prevArrow.addEventListener('touchstart', (e) => {
      prevArrowScrolling = false;
      prevTouchStartY = e.touches[0].clientY;
    });

    prevArrow.addEventListener('touchmove', (e) => {
      const touchMoveY = e.touches[0].clientY;
      const deltaY = Math.abs(touchMoveY - prevTouchStartY);

      if (deltaY > 10) {
        prevArrowScrolling = true;
      }
    });

    prevArrow.addEventListener('touchend', (e) => {
      if (prevArrowScrolling) {
        return;
      }

      e.preventDefault();
      slideTournaments('prev');
    });

    nextArrow.addEventListener('touchstart', (e) => {
      nextArrowScrolling = false;
      nextTouchStartY = e.touches[0].clientY;
    });

    nextArrow.addEventListener('touchmove', (e) => {
      const touchMoveY = e.touches[0].clientY;
      const deltaY = Math.abs(touchMoveY - nextTouchStartY);

      if (deltaY > 10) {
        nextArrowScrolling = true;
      }
    });

    nextArrow.addEventListener('touchend', (e) => {
      if (nextArrowScrolling) {
        return;
      }

      e.preventDefault();
      slideTournaments('next');
    });

    // Mouse drag event listeners
    tournamentsTrack.addEventListener('mousedown', function(e) {
      e.preventDefault();
      startDrag(e.clientX);
    });

    document.addEventListener('mousemove', function(e) {
      if (isDragging) {
        e.preventDefault();
        updateDrag(e.clientX);
      }
    });

    document.addEventListener('mouseup', function(e) {
      if (isDragging) {
        e.preventDefault();
        endDrag();
      }
    });

    // Touch drag event listeners for mobile with gesture detection
    tournamentsTrack.addEventListener('touchstart', function(e) {
      if (e.touches.length === 1) {
        // Initialize touch tracking
        touchStartX = e.touches[0].clientX;
        touchStartY = e.touches[0].clientY;
        gestureDetected = false;
        isVerticalGesture = false;
        touchStartedOnSlider = true;

        // Start as horizontal gesture by default - but don't prevent default yet
        // e.preventDefault(); // Don't block scroll on touchstart
        startDrag(e.touches[0].clientX);
      }
    }, { passive: false });

    container.addEventListener('touchmove', function(e) {
      // Only handle touch events that started on the slider
      if (e.touches.length === 1 && touchStartedOnSlider) {
        const currentX = e.touches[0].clientX;
        const currentY = e.touches[0].clientY;

        // If we haven't detected gesture direction yet
        if (!gestureDetected) {
          const deltaX = Math.abs(currentX - touchStartX);
          const deltaY = Math.abs(currentY - touchStartY);

          // Check if movement exceeds threshold
          if (deltaX > GESTURE_THRESHOLD || deltaY > GESTURE_THRESHOLD) {
            gestureDetected = true;
            isVerticalGesture = deltaY > deltaX;

            // If vertical gesture detected, stop dragging and allow scrolling
            if (isVerticalGesture) {
              endDrag();
              touchStartedOnSlider = false; // Stop handling this touch sequence
              return; // Don't prevent default, allow vertical scrolling
            }
          }
        }

        // If we're in horizontal drag mode (not vertical), continue dragging
        if (isDragging && !isVerticalGesture) {
          e.preventDefault();
          updateDrag(currentX);
        }
      }
    }, { passive: false });

    container.addEventListener('touchend', function(e) {
      // Only handle if touch started on slider
      if (touchStartedOnSlider) {
        if (isDragging && !isVerticalGesture) {
          e.preventDefault();
          endDrag();
        }

        // Reset gesture detection state
        gestureDetected = false;
        isVerticalGesture = false;
        touchStartedOnSlider = false;
      }
    }, { passive: false });
    
    // Handle window resize
    let resizeTimeout;
    const handleResize = () => {
      clearTimeout(resizeTimeout);
      resizeTimeout = setTimeout(() => {
        // Maintain current slide position
        const translateX = -currentSlide * 100;
        tournamentsTrack.style.transform = `translateX(${translateX}%)`;
        updateSliderControls();
      }, 250);
    };
    const rafThrottledHandleResize = rafThrottle(handleResize);
    
    window.addEventListener('resize', rafThrottledHandleResize);
    
    // Initialize
    loadTournaments();

    // Start countdown updates
    const countdownInterval = setInterval(updateCountdowns, 1000);

    // Cleanup
    window.addEventListener('@makrobet/unload/landing', () => {
      window.removeEventListener('resize', rafThrottledHandleResize);
      if (resizeTimeout) clearTimeout(resizeTimeout);
      if (countdownInterval) clearInterval(countdownInterval);
      kill();
    }, { once: true });
  }
});
